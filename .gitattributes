.babelrc export-ignore
.distignore export-ignore
.DS_Store export-ignore
.editorconfig export-ignore
.git export-ignore
.gitattributes export-ignore
.github export-ignore
.gitignore export-ignore
.gitlab-ci.yml export-ignore
.phplint.yml export-ignore
.travis.yml export-ignore
.vscode export-ignore
*.bash export-ignore
*.cache export-ignore
*.code-workspace export-ignore
*.dist export-ignore
*.dist.yml export-ignore
*.exe export-ignore
*.sh export-ignore
*.sql export-ignore
*.tar.gz export-ignore
*.testing export-ignore
*.zip export-ignore
/dist export-ignore
/screenshot-*.png export-ignore
/tests export-ignore
behat.yml export-ignore
bin export-ignore
builder.yml export-ignore
circle.yml export-ignore
codeception.yml export-ignore
composer.json export-ignore
composer.lock export-ignore
docker-compose.yml export-ignore
Gruntfile.js export-ignore
Gruntfile.js export-ignore
jsconfig.json export-ignore
multisite.xml export-ignore
multisite.xml.dist export-ignore
node_modules export-ignore
package-lock.json export-ignore
package.json export-ignore
phpcs.ruleset.xml export-ignore
phpcs.xml export-ignore
phpcs.xml.dist export-ignore
phpmd.xml export-ignore
phpunit.xml export-ignore
phpunit.xml.dist export-ignore
psalm.xml export-ignore
README.md export-ignore
RoboFile.php export-ignore
Thumbs.db export-ignore
webpack.config.js export-ignore
wp-cli.local.yml export-ignore
/dev-workspace export-ignore
.rsync-filters-pre-build export-ignore
.rsync-filters-post-build export-ignore
jest.config.js export-ignore
jest.config.ts export-ignore
.phpcs.xml export-ignore
webpack.common.js export-ignore
webpack.dev.js export-ignore
webpack.prod.js export-ignore
