# Translation of the WordPress plugin   by .
# Copyright (C) 2010
# This file is distributed under the same license as the  package.
# <AUTHOR> <EMAIL>, 2010.
msgid ""
msgstr ""
"Project-Id-Version: Publishpress Series Core Plugin\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-08-31 17:39+0000\n"
"PO-Revision-Date: 2022-04-11 14:58+0000\n"
"Last-Translator: \n"
"Language-Team: English (United States)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: _e;__;_n;_x;_ngettext:1;2\n"
"X-Poedit-Basepath: "
"/Users/<USER>/Documents/Dropbox/WebProjects/htdocs/orgseries-testing/wp-"
"content/plugins/organize-series\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Poedit-SearchPath-0: "
"/Users/<USER>/Documents/Dropbox/WebProjects/htdocs/orgseries-testing/wp-"
"content/plugins/organize-series\n"
"Language: en_US\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Loco-Version: 2.5.3; wp-5.8"

#: orgSeries-widgets.php:279
msgid " Dropdown: "
msgstr ""

#: orgSeries-widgets.php:280
msgid " List: "
msgstr ""

#: src/domain/exceptions/InvalidEntityException.php:29
#, php-format
msgid "%1$s is not a valid entity (expected: %2$s)."
msgstr ""

#: orgSeries-options.php:139 orgSeries-admin.php:46
#, php-format
msgid "%1$s series migrated to new taxonomy"
msgstr ""

#: src/domain/exceptions/InvalidInterfaceException.php:26
#, php-format
msgid "%s does not exist or is not reachable."
msgstr ""

#: orgSeries-admin.php:409 orgSeries-admin.php:417
#, php-format
msgid ""
"<a href=\"%1$s\" title=\"%2$s\">%3$s</a> - (Currently has no Part number)"
msgstr ""

#: orgSeries-admin.php:419
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> - (currently set as Part %4$s)"
msgstr ""

#: orgSeries-template-tags.php:204 orgSeries-template-tags.php:214
#, php-format
msgid "<a href=\"%s\" title=\"%s\">Series</a>"
msgstr ""

#: orgSeries-admin.php:343
msgid ""
"A short title of this post that will be used in the Series widget. Leave "
"blank to use the full title."
msgstr ""

#: orgSeries-admin.php:138
msgid "About"
msgstr ""

#: orgSeries-admin.php:138
msgid "About PublishPress Series"
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:33
#: src/domain/services/admin/LicenseKeyFormManager.php:123
msgid "Activate License"
msgstr ""

#: orgSeries-admin.php:323 orgSeries-setup.php:228
msgid "Add New"
msgstr ""

#: orgSeries-options.php:76 orgSeries-setup.php:285
msgid "Add New Series"
msgstr ""

#: orgSeries-options.php:241
msgid "Advanced"
msgstr ""

#: orgSeries-options.php:73 orgSeries-setup.php:282 orgSeries-setup.php:322
msgid "All Series"
msgstr ""

#: orgSeries-template-tags.php:200
msgid "All the Series I've Written"
msgstr ""

#: orgSeries-options.php:283
msgid "Allowed Html"
msgstr ""

#: orgSeries-admin.php:43
msgid "An error occured"
msgstr ""

#: src/domain/model/LicenseKeyRepository.php:131
msgid "An error occurred, please try again."
msgstr ""

#: orgSeries-options.php:572
msgid "As in Template"
msgstr ""

#: orgSeries-widgets.php:100
msgid "ASC: "
msgstr ""

#: orgSeries-widgets.php:319 orgSeries-options.php:527
msgid "Ascending"
msgstr ""

#: orgSeries-utility.php:405
msgid "Auto/None"
msgstr ""

#: orgSeries-options.php:866
msgid "Automatic Numbering"
msgstr ""

#: orgSeries-options.php:574
msgid "Bottom"
msgstr ""

#: orgSeries-options.php:432
msgid "Choose the design for pages that are included in a series."
msgstr ""

#: orgSeries-options.php:533
msgid "Choose the design for the page where all your Series are displayed."
msgstr ""

#: orgSeries-options.php:469
msgid ""
"Choose the design for the taxonomy page where a single Series is displayed."
msgstr ""

#. phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
#: orgSeries-options.php:493
#, php-format
msgid ""
"Choosing a layout different to \"Default\" will override the taxonomy "
"template from your theme. <a href=\"%s\" target=\"_blank\">Click here for "
"details on how to customize these designs</a>."
msgstr ""

#: orgSeries-options.php:360
msgid ""
"Clicking Yes will reset the options to the defaults and you will lose all "
"customizations. Or you can click cancel and return."
msgstr ""

#: orgSeries-options.php:503
msgid "Columns:"
msgstr ""

#: orgSeries-admin.php:140
msgid "Contact"
msgstr ""

#: orgSeries-admin.php:140
msgid "Contact the PublishPress team"
msgstr ""

#: orgSeries-widgets.php:299
msgid "count"
msgstr ""

#: orgSeries-widgets.php:396
msgid "Current series"
msgstr ""

#: orgSeries-manage.php:115
msgid "Current series icon:"
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:31
#: src/domain/services/admin/LicenseKeyFormManager.php:122
msgid "Deactivate License"
msgstr ""

#: orgSeries-options.php:422
msgid "Default"
msgstr ""

#: orgSeries-options.php:887
msgid ""
"Delete all PublishPress Series data from the database when deleting this "
"plugin."
msgstr ""

#: orgSeries-manage.php:131
msgid ""
"Delete image? (note: there will not be an image associated with this series "
"if you select this)"
msgstr ""

#: orgSeries-widgets.php:101
msgid "DESC: "
msgstr ""

#: orgSeries-widgets.php:318 orgSeries-options.php:528
msgid "Descending"
msgstr ""

#: orgSeries-options.php:468
msgid "Display on Series Overview screens"
msgstr ""

#: orgSeries-options.php:532
msgid "Display on Series Table of Contents screens"
msgstr ""

#: orgSeries-options.php:431
msgid "Display on single posts in a series"
msgstr ""

#: orgSeries-widgets.php:99
msgid "Display Order: "
msgstr ""

#: orgSeries-options.php:449
msgid "Display Series Meta?"
msgstr ""

#: orgSeries-options.php:445
msgid "Display Series Navigation?"
msgstr ""

#: orgSeries-options.php:437
msgid "Display Series Post List?"
msgstr ""

#: orgSeries-admin.php:139
msgid "Documentation"
msgstr ""

#: orgSeries-options.php:74 orgSeries-setup.php:283
msgid "Edit Series"
msgstr ""

#: orgSeries-options.php:872
msgid "Enable automatic renumbering of posts in a series."
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:19
msgid "Enter your license key: "
msgstr ""

#: orgSeries-widgets.php:332
msgid "Exclude series"
msgstr ""

#: orgSeries-options.php:699
msgid "First Post"
msgstr ""

#: orgSeries-options.php:423
msgid "Grid"
msgstr ""

#: orgSeries-widgets.php:289
msgid "Hide empty series"
msgstr ""

#: orgSeries-widgets.php:82
msgid "Hide series with no posts?"
msgstr ""

#: orgSeries-admin.php:186
msgid ""
"Hmm... it looks like there is already a series with that name. Try something "
"else"
msgstr ""

#. Author URI of the plugin
msgid "https://publishpress.com/"
msgstr ""

#. URI of the plugin
msgid "https://publishpress.com/publishpress-series/"
msgstr ""

#: orgSeries-manage.php:60
msgid "Icon"
msgstr ""

#: orgSeries-manage.php:61
msgid "ID"
msgstr ""

#: orgSeries-admin.php:336
msgid ""
"If you leave this blank, this post will automatically be added to the end of "
"the series."
msgstr ""

#: orgSeries-admin.php:130
#, php-format
msgid "If you like %s, please leave us a %s rating. Thank you!"
msgstr ""

#: orgSeries-widgets.php:347
msgid "Include series"
msgstr ""

#: orgSeries-options.php:308
msgid ""
"Is the location token for where the contents of the post list post templates "
"will appear and use provided widget post short title."
msgstr ""

#: orgSeries-options.php:306
msgid ""
"Is the location token for where the contents of the post list post templates "
"will appear."
msgstr ""

#: orgSeries-widgets.php:13 orgSeries-options.php:707
msgid "Latest Series"
msgstr ""

#: orgSeries-options.php:718
msgid "Latest Series (inner tags):"
msgstr ""

#: orgSeries-options.php:723
msgid "Latest Series (tags after):"
msgstr ""

#: orgSeries-options.php:713
msgid "Latest Series (tags before):"
msgstr ""

#: orgSeries-options.php:475
msgid "Layout:"
msgstr ""

#: orgSeries-setup.php:328 orgSeries-setup.php:329
msgctxt "leave the %tokens% as is when translating"
msgid ""
"This entry is part %series_part% of %total_posts_in_series% in the series "
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:16
#, php-format
msgid "License Key for %1$s"
msgstr ""

#: src/domain/services/admin/LicenseKeyFormManager.php:241
msgid "License Key successfully activated."
msgstr ""

#: src/domain/services/admin/LicenseKeyFormManager.php:242
msgid "License Key successfully deactivated."
msgstr ""

#: orgSeries-options.php:424
msgid "List"
msgstr ""

#: orgSeries-admin.php:445
msgid "Manage All Series"
msgstr ""

#: orgSeries-options.php:78 orgSeries-setup.php:287
msgid "Manage Series"
msgstr ""

#: includes-core/pro-placeholder/views/series-group-placeholder.php:2
msgid "Manage Series Groups"
msgstr ""

#: includes-core/pro-placeholder/views/publish-series-placeholder.php:2
msgid "Manage Series to Publish"
msgstr ""

#: orgSeries-options.php:441
msgid "Maximum number of items in Series Post List"
msgstr ""

#: orgSeries-options.php:240
msgid "Metabox"
msgstr ""

#: orgSeries-options.php:813
msgid "Migrate"
msgstr ""

#: orgSeries-options.php:817
msgid "Migrate series to new taxonomy"
msgstr ""

#: orgSeries-widgets.php:20
msgid "Most Recent Series"
msgstr ""

#: inc/utility-functions.php:33
#, php-format
msgid ""
"Most web hosts provide an easy path to update the php version on your "
"website.  We recommend updating to PHP 7 or greater. Before you update, you "
"will want to make sure other plugins and your theme are compatible (see "
"%1$sthis article for more info%2$s)."
msgstr ""

#: orgSeries-widgets.php:298
msgid "name"
msgstr ""

#: orgSeries-widgets.php:88
msgid "Name of Series"
msgstr ""

#: orgSeries-options.php:77 orgSeries-setup.php:286
msgid "New Series Name"
msgstr ""

#: orgSeries-options.php:689
msgid "Next Post"
msgstr ""

#: orgSeries-options.php:361
msgid "No"
msgstr ""

#: orgSeries-manage.php:121
msgid "No icon currently"
msgstr ""

#: orgSeries-admin.php:425
msgid "No Series"
msgstr ""

#: orgSeries-options.php:79 orgSeries-setup.php:288
msgid "No series found"
msgstr ""

#: orgSeries-manage.php:75
msgid "No Series Icon"
msgstr ""

#: src/domain/exceptions/NonceFailException.php:18
msgid "Nonce fail."
msgstr ""

#: orgSeries-admin.php:289
msgid "Not part of a series"
msgstr ""

#: orgSeries-widgets.php:87
msgid "Number of posts in Series"
msgstr ""

#: orgSeries-widgets.php:362
msgid "Number of Series"
msgstr ""

#: orgSeries-widgets.php:95
msgid "Number of series to display:"
msgstr ""

#: orgSeries-widgets.php:367
msgid "Offset"
msgstr ""

#: orgSeries-widgets.php:314
msgid "Order\t"
msgstr ""

#: orgSeries-widgets.php:294
msgid "Order by\t"
msgstr ""

#: orgSeries-options.php:521
msgid "Order by date"
msgstr ""

#: orgSeries-options.php:518
msgid "Order series by:"
msgstr ""

#: orgSeries-widgets.php:233
msgid "Other posts in series:"
msgstr ""

#: orgSeries-options.php:292
msgid "Overview"
msgstr ""

#: orgSeries-admin.php:412
#, php-format
msgid ""
"Part %1$s of %2$s%6$s in the series <br/><a href=\"%3$s\" title=\"%4$s\">"
"%5$s</a>"
msgstr ""

#: orgSeries-taxonomy.php:712
msgid "Part:"
msgstr ""

#: orgSeries-options.php:101
msgid "Permission denied"
msgstr ""

#: orgSeries-options.php:396
msgid ""
"Please change these settings carefully as they make significant changes to "
"PublishPress Series."
msgstr ""

#: orgSeries-options.php:819
msgid ""
"Please use with caution. Running this process will delete all the terms from "
"the current taxonomy and migrate them to a new taxonomy."
msgstr ""

#: orgSeries-options.php:72 orgSeries-setup.php:281
msgid "Popular Series"
msgstr ""

#: orgSeries-admin.php:341
msgid "Post title in widget:"
msgstr ""

#: orgSeries-setup.php:718
msgid "Posts from the series: "
msgstr ""

#: orgSeries-options.php:694
msgid "Previous Post"
msgstr ""

#: includes-core/PPSeriesCoreAdmin.php:46
#: includes-core/PPSeriesCoreAdmin.php:47
msgid "Publish Series"
msgstr ""

#. Author of the plugin
msgid "PublishPress"
msgstr ""

#. Name of the plugin
msgid "PublishPress Series"
msgstr ""

#: inc/utility-functions.php:24
#, php-format
msgid ""
"Publishpress Series %1$srequires PHP 5.6%2$s or greater.  Your website does "
"not meet the requirements so the plugin is not fully activated."
msgstr ""

#. Description of the plugin
msgid ""
"PublishPress Series allows you to group content together into a series. This "
"is ideal for magazines, newspapers, short-story writers, teachers, comic "
"artists, or anyone who writes multiple posts on the same topic."
msgstr ""

#: orgSeries-admin.php:139
msgid "PublishPress Series Documentation"
msgstr ""

#: orgSeries-options.php:35
msgid "PublishPress Series Options"
msgstr ""

#: orgSeries-options.php:253
msgid "PublishPress Series Plugin Options"
msgstr ""

#: orgSeries-options.php:108
msgid "PublishPress Series Plugin Options have been RESET"
msgstr ""

#: orgSeries-options.php:143
msgid "PublishPress Series Plugin Options have been updated"
msgstr ""

#: includes-core/pro-placeholder/views/publish-series-placeholder.php:11
msgid ""
"PublishPress Series Pro enables easy bulk publishing of all posts in a "
"series at once."
msgstr ""

#: includes-core/pro-placeholder/views/series-group-placeholder.php:11
msgid ""
"PublishPress Series Pro gives you the ability to group series together by "
"category."
msgstr ""

#: orgSeries-widgets.php:91
msgid "Random"
msgstr ""

#: orgSeries-options.php:897
msgid "Reset options to default"
msgstr ""

#: orgSeries-options.php:894
msgid "Reset settings"
msgstr ""

#: orgSeries-options.php:298
#, php-format
msgid ""
"Same as %series_icon% except that the series icon will be linked to the "
"series page"
msgstr ""

#: orgSeries-options.php:304
#, php-format
msgid ""
"Same as %series_title% except that it will also be linked to the series page"
msgstr ""

#: orgSeries-widgets.php:373
msgid "Search"
msgstr ""

#: orgSeries-options.php:71 orgSeries-setup.php:280
msgid "Search Series"
msgstr ""

#: orgSeries-setup.php:229
msgid "Select \"Not part of a series\" to remove any series data from post"
msgstr ""

#: orgSeries-widgets.php:177
msgid "Select Series"
msgstr ""

#: orgSeries-widgets.php:232 orgSeries-widgets.php:385
#: orgSeries-taxonomy.php:446 orgSeries-options.php:36 orgSeries-admin.php:358
#: orgSeries-admin.php:370 orgSeries-admin.php:453
msgid "Series"
msgstr ""

#: orgSeries-options.php:510
msgid "Series Custom Base:"
msgstr ""

#: includes-core/PPSeriesCoreAdmin.php:56
#: includes-core/PPSeriesCoreAdmin.php:57
msgid "Series Group"
msgstr ""

#: orgSeries-manage.php:136
msgid "Series Icon Upload:"
msgstr ""

#: orgSeries-widgets.php:301
msgid "series id"
msgstr ""

#: orgSeries-options.php:651
msgid "Series Meta (with excerpts):"
msgstr ""

#: orgSeries-options.php:624
msgid "Series Meta Box"
msgstr ""

#: orgSeries-options.php:630
msgid "Series Meta:"
msgstr ""

#: orgSeries-options.php:636
msgid "Series Metabox Location"
msgstr ""

#: orgSeries-setup.php:334
msgid "Series Navigation"
msgstr ""

#: orgSeries-options.php:663
msgid "Series Navigation Box"
msgstr ""

#: orgSeries-options.php:525
msgid "Series order method"
msgstr ""

#: orgSeries-options.php:520
msgid "Series part"
msgstr ""

#: orgSeries-admin.php:332
msgid "Series Part:"
msgstr ""

#: orgSeries-options.php:549
msgid "Series Per Page:"
msgstr ""

#: orgSeries-options.php:590
msgid "Series Post List"
msgstr ""

#: orgSeries-options.php:585
msgid "Series Post List Box"
msgstr ""

#: orgSeries-options.php:595
msgid "Series Post List box Location"
msgstr ""

#: orgSeries-options.php:616
msgid "Series Post List Post Title (Current Post)"
msgstr ""

#: orgSeries-options.php:610
msgid "Series Post List Post Title (Linked Post)"
msgstr ""

#: orgSeries-options.php:674
msgid "Series Post Navigation Location"
msgstr ""

#: orgSeries-options.php:669
msgid "Series Post Navigation:"
msgstr ""

#: orgSeries-options.php:881
msgid "Series Settings"
msgstr ""

#: orgSeries-widgets.php:89
msgid "Series Slug"
msgstr ""

#: orgSeries-widgets.php:112 orgSeries-options.php:731
#: orgSeries-options.php:737 orgSeries-setup.php:451
msgid "Series Table of Contents"
msgstr ""

#: orgSeries-options.php:553
msgid "Series Table of Contents Title:"
msgstr ""

#: orgSeries-options.php:535
msgid "Series Table of Contents URL:"
msgstr ""

#: orgSeries-options.php:802
msgid "Series Taxonomy:"
msgstr ""

#: orgSeries-widgets.php:415
msgid "Series widget title:"
msgstr ""

#: orgSeries-taxonomy.php:710 orgSeries-taxonomy.php:735
msgid "Series:"
msgstr ""

#: orgSeries-setup.php:716
msgid "Series: "
msgstr ""

#: orgSeries-options.php:24 orgSeries-options.php:45 orgSeries-options.php:46
#: orgSeries-setup.php:730
msgid "Settings"
msgstr ""

#: orgSeries-options.php:838
msgid "Show \"Add New\""
msgstr ""

#: orgSeries-options.php:846
msgid "Show \"Post title in widget\""
msgstr ""

#: orgSeries-options.php:842
msgid "Show \"Series Part\""
msgstr ""

#: orgSeries-widgets.php:379
msgid "Show other posts in the current series"
msgstr ""

#: orgSeries-widgets.php:284
msgid "Show post count"
msgstr ""

#: orgSeries-widgets.php:273
msgid "Show Series Table Of Content"
msgstr ""

#: orgSeries-widgets.php:278
msgid "Show Series Table Of Content as"
msgstr ""

#: orgSeries-widgets.php:300
msgid "slug"
msgstr ""

#: orgSeries-admin.php:169
msgid "Sorry but you don't have permission to add series"
msgstr ""

#: inc/templates/taxonomy-series.php:67
msgid "Sorry, no results found."
msgstr ""

#: orgSeries-options.php:457
msgid "Style options"
msgstr ""

#: orgSeries-options.php:239
msgid "Taxonomy"
msgstr ""

#: orgSeries-options.php:69 orgSeries-setup.php:278
msgctxt "taxonomy general name"
msgid "Series"
msgstr ""

#: orgSeries-options.php:70 orgSeries-setup.php:279
msgctxt "taxonomy singular name"
msgid "Series"
msgstr ""

#: src/domain/exceptions/InvalidFilePathException.php:27
#, php-format
msgid ""
"The \"%1$s\" file is either missing or could not be read due to permissions. "
"Please ensure that the following path is correct and verify that the file "
"permissions are correct:%2$s %3$s"
msgstr ""

#: src/application/Container.php:160
#, php-format
msgid "The %1$s already has a parameter indexed with the name: %2$s."
msgstr ""

#: src/application/Root.php:139
#, php-format
msgid "The %1$s method can only be used to register a child of %2$s."
msgstr ""

#: orgSeries-options.php:294
msgid ""
"The following is a legend of the tokens that are available for use in the "
"custom template fields. These will be replaced with the appropriate values "
"when the plugin runs."
msgstr ""

#: src/domain/model/FileLocation.php:50
#, php-format
msgid "The given file path (%s) is not readable."
msgstr ""

#: src/domain/model/RouteIdentifier.php:51
#, php-format
msgid "The incoming value for %1$s is expected to be a closure.  It was not."
msgstr ""

#: orgSeries-widgets.php:79
#, php-format
msgid ""
"The layout and content of this widget can be adjusted via the <a href=\"%s\">"
"Latest Series</a> area."
msgstr ""

#: orgSeries-admin.php:180
msgid ""
"The name you picked isn't sanitizing correctly. Try something different."
msgstr ""

#: src/domain/services/AssetRegistry.php:408
#, php-format
msgid ""
"The namespace for this manifest file has already been registered, choose a "
"namespace other than %s"
msgstr ""

#: src/domain/model/ControllerRoute.php:50
#: src/domain/model/HasHooksRoute.php:59
#, php-format
msgid ""
"The provided object fully qualified class name (%1$s) must implement the "
"%2$s interface."
msgstr ""

#: src/domain/services/AssetRegistry.php:419
#, php-format
msgid ""
"The provided value for %1$s is not a valid url.  The url provided was: %2$s"
msgstr ""

#: src/domain/services/AssetRegistry.php:283
#, php-format
msgid ""
"The value for %1$s already exists in the %2$s property. Overrides are not "
"allowed.  Consider attaching your value to a different key"
msgstr ""

#: src/domain/services/AssetRegistry.php:271
#, php-format
msgid ""
"The value for %1$s already exists in the %2$s property. Overrides are not "
"allowed. Since the value of this data is an array, you may want to use the "
"%3$s method to push your value to the array."
msgstr ""

#: src/domain/services/AssetRegistry.php:228
#, php-format
msgid ""
"The value for %1$s is already set and it is not an array. The %2$s method "
"can only be used to push values to this data element when it is an array."
msgstr ""

#: orgSeries-options.php:375
msgid ""
"These settings allow you to customize the main frontend screens in "
"PublishPress Series."
msgstr ""

#: orgSeries-options.php:410
msgid ""
"These settings allow you to customize the metabox on the post editing screen."
msgstr ""

#: orgSeries-options.php:382
msgid ""
"These templates allow you to customize the frontend appearance of "
"PublishPress Series."
msgstr ""

#: orgSeries-options.php:655
msgid ""
"This control how and what series meta information is displayed with posts "
"that are part of a series when the_excerpt is used. "
msgstr ""

#: orgSeries-options.php:665
msgid "This display is shown at the bottom of all posts in a series."
msgstr ""

#: orgSeries-options.php:587 orgSeries-options.php:626
msgid "This display is shown at the top of all posts in a series."
msgstr ""

#: orgSeries-options.php:709
msgid "This display is used by the \"Latest Series\" widget."
msgstr ""

#: orgSeries-options.php:733
msgid ""
"This display is used by the \"Series Table of Contents\" widget, shortcode, "
"and URL."
msgstr ""

#: orgSeries-options.php:740
msgid ""
"This display is used by the \"Series Table of Contents\". To find the URL "
"for this display, go the \"Display\" tab and then \"Series Table of Contents "
"URL\"."
msgstr ""

#: orgSeries-options.php:403
msgid ""
"This feature allows you to create a new taxonomy for this plugin to use if "
"you don't want to use the default \"Series\" taxonomy."
msgstr ""

#: orgSeries-options.php:389
msgid ""
"This section is for the icons that show with your series. Note that you must "
"use a token for the icon in the \"Templates\" settings."
msgstr ""

#: orgSeries-options.php:513
msgid "This text will be part of the URL for all Series Overview pages."
msgstr ""

#: orgSeries-widgets.php:417
msgid "This text will display above other posts in this series."
msgstr ""

#: orgSeries-options.php:300
msgid ""
"This token is for use with the orgSeries widget only - it references where "
"you want the list of series titles to be inserted and requires that the "
"template for each series title be also set."
msgstr ""

#: orgSeries-options.php:296
msgid "This will be replaced with the series icon for a series."
msgstr ""

#: orgSeries-options.php:302
msgid "This will be replaced with the title of a series"
msgstr ""

#: orgSeries-widgets.php:73 orgSeries-widgets.php:268
msgid "Title"
msgstr ""

#: orgSeries-options.php:806
msgid ""
"To create a new taxonomy, enter the new name and click the \"Update "
"Options\" button."
msgstr ""

#: inc/utility-functions.php:44
msgid ""
"To remove this notice you can either deactivate the plugin or upgrade the "
"php version on your server."
msgstr ""

#: orgSeries-options.php:573
msgid "Top"
msgstr ""

#: src/domain/exceptions/EntityNotFoundException.php:30
#, php-format
msgid "Unable to retrieve an instance of %1$s. Not found."
msgstr ""

#: orgSeries-options.php:356
msgid "Update Options"
msgstr ""

#: orgSeries-options.php:75 orgSeries-setup.php:284
msgid "Update Series"
msgstr ""

#: includes-core/pro-placeholder/views/publish-series-placeholder.php:15
#: includes-core/pro-placeholder/views/series-group-placeholder.php:15
msgid "Upgrade to Pro"
msgstr ""

#: orgSeries-manage.php:96 orgSeries-manage.php:140
msgid "Upload an image for the series."
msgstr ""

#: orgSeries-options.php:460
msgid "Use box style"
msgstr ""

#: orgSeries-options.php:461
msgid "Use dark style"
msgstr ""

#: orgSeries-options.php:459
msgid "Use default style"
msgstr ""

#: orgSeries-options.php:462
msgid "Use light style"
msgstr ""

#: orgSeries-options.php:453
msgid "Use PublishPress Series CSS styles?"
msgstr ""

#: orgSeries-options.php:320
msgid ""
"Use this tag either before or after the rest of the template code.  It will "
"indicate where you want the content of a post to display."
msgstr ""

#: orgSeries-widgets.php:12
msgid "Use this to control the output of the latest series widget"
msgstr ""

#: orgSeries-widgets.php:111
msgid "Use this to display the Series Table of contents"
msgstr ""

#: orgSeries-admin.php:438
msgid "View all series"
msgstr ""

#: orgSeries-widgets.php:90
msgid "When Series was Created"
msgstr ""

#: orgSeries-options.php:779
msgid "Width for icon if displayed via the latest series template (in pixels)."
msgstr ""

#: orgSeries-options.php:773
msgid "Width for icon on a post page (in pixels)."
msgstr ""

#: orgSeries-options.php:767
msgid "Width for icon on series table of contents page (in pixels)"
msgstr ""

#: orgSeries-options.php:318
msgid ""
"Will be replaced by the navigation link for the first post in a series. The "
"text will be whatever is included in the 'Custom First Post Navigation Text' "
"field. If that field is empty then the text will be the title of the post"
msgstr ""

#: orgSeries-options.php:316
msgid ""
"Will be replaced by the navigation link for the next post in a series. The "
"text will be whatever is included in the 'Custom Next Post Navigation Text' "
"field. If that field is empty then the text will be the title of the post"
msgstr ""

#: orgSeries-options.php:314
msgid ""
"Will be replaced by the navigation link for the previous post in a series. "
"The text will be whatever is included in the 'Custom Previous Post "
"Navigation Text' field. If that field is empty then the text will be the "
"title of the post"
msgstr ""

#: orgSeries-options.php:310
msgid "Will be replaced with the post title of a post in the series"
msgstr ""

#: orgSeries-options.php:312
msgid ""
"Will be replaced with the post title of a post in the series linked to the "
"page view of that post."
msgstr ""

#: orgSeries-options.php:326
msgid "Will display the description for the series"
msgstr ""

#: orgSeries-options.php:324
msgid "Will display the total number of posts in a series"
msgstr ""

#: orgSeries-options.php:322
msgid "Will display what part of a series the post is"
msgstr ""

#: orgSeries-options.php:362
msgid "Yes"
msgstr ""

#: orgSeries-widgets.php:375
msgid ""
"You can return any series that match this search string (matched against "
"series names) - case insensitive"
msgstr ""

#: orgSeries-widgets.php:369
msgid ""
"You can select the offset for the number of series (useful for paging).  No "
"offset if left blank"
msgstr ""

#: orgSeries-setup.php:102
msgid ""
"Your WordPress version is too old. Publishpress Series 2.2 requires at least "
"WordPress 3.0 to function correctly. Please update your blog via Tools &gt; "
"Upgrade."
msgstr ""
