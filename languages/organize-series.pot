# Translation of the WordPress plugin   by .
# Copyright (C) 2010
# This file is distributed under the same license as the  package.
# <AUTHOR> <EMAIL>, 2010.
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Publishpress Series Core Plugin\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-07 08:25+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: _e;__;_n;_x;_ngettext:1;2\n"
"X-Poedit-Basepath: "
"/Users/<USER>/Documents/Dropbox/WebProjects/htdocs/orgseries-testing/wp-"
"content/plugins/organize-series\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Poedit-SearchPath-0: "
"/Users/<USER>/Documents/Dropbox/WebProjects/htdocs/orgseries-testing/wp-"
"content/plugins/organize-series\n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;"

#: src/domain/exceptions/InvalidEntityException.php:29
#, php-format
msgid "%1$s is not a valid entity (expected: %2$s)."
msgstr ""

#: orgSeries-options.php:148 orgSeries-admin.php:70
#, php-format
msgid "%1$s series migrated to new taxonomy"
msgstr ""

#: src/domain/exceptions/InvalidInterfaceException.php:26
#, php-format
msgid "%s does not exist or is not reachable."
msgstr ""

#: orgSeries-setup.php:252
msgid "&larr; Go to Series"
msgstr ""

#: addons/publisher/series-part-post-table.php:304
msgid "(Currently has no Part number)"
msgstr ""

#: orgSeries-admin.php:493
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a>  (No Part Number)"
msgstr ""

#: orgSeries-admin.php:487
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (No part number)"
msgstr ""

#: orgSeries-admin.php:489
#, php-format
msgid "<a href=\"%3$s\" title=\"%4$s\">%5$s</a> (Part %1$s of %2$s)"
msgstr ""

#: orgSeries-template-tags.php:220 orgSeries-template-tags.php:229
#, php-format
msgid "<a href=\"%s\" title=\"%s\">Series</a>"
msgstr ""

#: orgSeries-setup.php:257
msgid "A link to a Series"
msgstr ""

#: orgSeries-admin.php:430
msgid ""
"A short title of this post that will be used in the Series widget. Leave "
"blank to use the full title."
msgstr ""

#: orgSeries-admin.php:171
msgid "About"
msgstr ""

#: orgSeries-admin.php:171
msgid "About PublishPress Series"
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:33
#: src/domain/services/admin/LicenseKeyFormManager.php:123
msgid "Activate License"
msgstr ""

#: orgSeries-admin.php:398 orgSeries-setup.php:191
msgid "Add New"
msgstr ""

#: orgSeries-options.php:85 orgSeries-setup.php:248
msgid "Add New Series"
msgstr ""

#: addons/grouping/legacy-includes.php:223
msgid "Add New Series Category"
msgstr ""

#: addons/publisher/series_im_admin_main.php:34
msgid "Add new Series."
msgstr ""

#: orgSeries-options.php:273
msgid "Advanced"
msgstr ""

#: addons/publisher/series-publish-post-table.php:177
msgid "All Categories"
msgstr ""

#: orgSeries-options.php:82 orgSeries-setup.php:245 orgSeries-setup.php:291
msgid "All Series"
msgstr ""

#: addons/grouping/legacy-includes.php:220
msgid "All Series Categories"
msgstr ""

#: orgSeries-template-tags.php:216
msgid "All the Series I've Written"
msgstr ""

#: orgSeries-options.php:319
msgid "Allowed Html"
msgstr ""

#: orgSeries-options.php:1075
msgid "Alphabetical A-Z"
msgstr ""

#: orgSeries-options.php:1076
msgid "Alphabetical Z-A"
msgstr ""

#: orgSeries-admin.php:67
msgid "An error occured"
msgstr ""

#: src/domain/model/LicenseKeyRepository.php:131
msgid "An error occurred, please try again."
msgstr ""

#: orgSeries-options.php:796
msgid "As in Template"
msgstr ""

#: orgSeries-widgets.php:100
msgid "ASC: "
msgstr ""

#: orgSeries-widgets.php:320 orgSeries-options.php:731
msgid "Ascending"
msgstr ""

#: addons/publisher/series-publish-post-table.php:73
#: addons/publisher/series-pending-post-table.php:66
#: addons/publisher/series-part-post-table.php:63
msgid "Author"
msgstr ""

#: orgSeries-utility.php:410
msgid "Auto/None"
msgstr ""

#: orgSeries-options.php:798
msgid "Bottom"
msgstr ""

#: addons/publisher/series-publish-post-table.php:74
#: addons/publisher/series-pending-post-table.php:67
#: addons/publisher/series-part-post-table.php:64
msgid "Categories"
msgstr ""

#: orgSeries-options.php:644
msgid "Choose the design for pages that are included in a series."
msgstr ""

#: orgSeries-options.php:737
msgid "Choose the design for the page where all your Series are displayed."
msgstr ""

#: orgSeries-options.php:681
msgid ""
"Choose the design for the taxonomy page where a single Series is displayed."
msgstr ""

#. phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
#: orgSeries-options.php:705
#, php-format
msgid ""
"Choosing a layout different to \"Default\" will override the taxonomy "
"template from your theme. <a href=\"%s\" target=\"_blank\">Click here for "
"details on how to customize these designs</a>."
msgstr ""

#: orgSeries-options.php:572
msgid ""
"Clicking Yes will reset the options to the defaults and you will lose all "
"customizations. Or you can click cancel and return."
msgstr ""

#: orgSeries-options.php:715
msgid "Columns:"
msgstr ""

#: addons/publisher/series_issue_manager.php:307
#: addons/publisher/series_issue_manager.php:344
msgid "Congratulations. Your posts were published successfully."
msgstr ""

#: addons/publisher/series_issue_manager.php:339
msgid "Congratulations. Your posts were scheduled successfully."
msgstr ""

#: addons/publisher/series_issue_manager.php:299
#: addons/publisher/series_issue_manager.php:416
msgid "Congratulations. Your series order was updated successfully."
msgstr ""

#: orgSeries-admin.php:173
msgid "Contact"
msgstr ""

#: orgSeries-admin.php:173
msgid "Contact the PublishPress team"
msgstr ""

#: orgSeries-widgets.php:300
msgid "count"
msgstr ""

#: addons/publisher/series_issue_manager.php:209
msgid "Create as unpublished:"
msgstr ""

#: addons/publisher/series-pending-post-table.php:70
#: addons/publisher/series-part-post-table.php:67
msgid "Current Part"
msgstr ""

#: orgSeries-widgets.php:397
msgid "Current series"
msgstr ""

#: orgSeries-manage.php:251
msgid "Current series icon:"
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:31
#: src/domain/services/admin/LicenseKeyFormManager.php:122
msgid "Deactivate License"
msgstr ""

#: orgSeries-options.php:634
msgid "Default"
msgstr ""

#: orgSeries-options.php:1074
msgid "Default Series Order"
msgstr ""

#: addons/grouping/legacy-includes.php:353
msgid ""
"Delete all \"Series Categories\" data from the database when deleting the "
"plugin."
msgstr ""

#: orgSeries-options.php:1133
msgid ""
"Delete all PublishPress Series data from the database when deleting this "
"plugin."
msgstr ""

#: orgSeries-manage.php:267
msgid ""
"Delete image? (note: there will not be an image associated with this series "
"if you select this)"
msgstr ""

#: orgSeries-widgets.php:101
msgid "DESC: "
msgstr ""

#: orgSeries-widgets.php:319 orgSeries-options.php:732
msgid "Descending"
msgstr ""

#: orgSeries-utility.php:308
msgid "Display"
msgstr ""

#: orgSeries-options.php:680
msgid "Display on Series Overview screens"
msgstr ""

#: orgSeries-options.php:736
msgid "Display on Series Table of Contents screens"
msgstr ""

#: orgSeries-options.php:643
msgid "Display on single posts in a series"
msgstr ""

#: orgSeries-widgets.php:99
msgid "Display Order: "
msgstr ""

#: orgSeries-options.php:661
msgid "Display Series Meta?"
msgstr ""

#: orgSeries-options.php:657
msgid "Display Series Navigation?"
msgstr ""

#: orgSeries-options.php:649
msgid "Display Series Post List?"
msgstr ""

#: orgSeries-admin.php:172
msgid "Documentation"
msgstr ""

#: addons/publisher/series_issue_manager.php:795
msgid ""
"Drag the post names into the order you want them to be in the series, from "
"the first part to the last part."
msgstr ""

#: orgSeries-widgets.php:280
msgid "Dropdown:"
msgstr ""

#: addons/publisher/series-publish-post-table.php:271
#: addons/publisher/series-pending-post-table.php:144
#: addons/publisher/series-part-post-table.php:141
msgid "Edit"
msgstr ""

#: orgSeries-options.php:83 orgSeries-setup.php:246
msgid "Edit Series"
msgstr ""

#: addons/grouping/legacy-includes.php:221
msgid "Edit Series Category"
msgstr ""

#: addons/publisher/series_im_admin_main.php:17
#, php-format
msgid "Edit the status of %1$s"
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:19
msgid "Enter your license key: "
msgstr ""

#: orgSeries-widgets.php:333
msgid "Exclude series:"
msgstr ""

#: addons/publisher/series-publish-post-table.php:190
msgid "Filter"
msgstr ""

#: orgSeries-options.php:928
msgid "First Post"
msgstr ""

#: addons/publisher/series_im_admin_main.php:10
msgid "Front View"
msgstr ""

#: orgSeries-options.php:635
msgid "Grid"
msgstr ""

#: orgSeries-widgets.php:290
msgid "Hide empty series:"
msgstr ""

#: orgSeries-widgets.php:82
msgid "Hide series with no posts?"
msgstr ""

#: orgSeries-admin.php:221
msgid ""
"Hmm... it looks like there is already a series with that name. Try something "
"else"
msgstr ""

#: orgSeries-utility.php:309
msgid "Icons"
msgstr ""

#: orgSeries-manage.php:184
msgid "ID"
msgstr ""

#: orgSeries-options.php:503
msgid "If the post has a feature-image then that image will be displayed"
msgstr ""

#: orgSeries-admin.php:160
#, php-format
msgid "If you like %s, please leave us a %s rating. Thank you!"
msgstr ""

#: orgSeries-options.php:1149
msgid ""
"In version 2.11.4, PublishPress Series made changes to how series are stored."
" You can run the upgrade task here if you're having issues with series parts."
msgstr ""

#: orgSeries-widgets.php:348
msgid "Include series:"
msgstr ""

#: orgSeries-manage.php:291
msgid "Invalid form data"
msgstr ""

#: orgSeries-manage.php:301
msgid "Invalid nonce, reload and try again"
msgstr ""

#: orgSeries-manage.php:310
msgid "Invalid taxonomy"
msgstr ""

#: orgSeries-options.php:382
msgid ""
"Is the location token for where the contents of the post list post templates "
"will appear and use provided widget post short title."
msgstr ""

#: orgSeries-options.php:374
msgid ""
"Is the location token for where the contents of the post list post templates "
"will appear."
msgstr ""

#: orgSeries-widgets.php:13 orgSeries-options.php:936
msgid "Latest Series"
msgstr ""

#: orgSeries-options.php:947
msgid "Latest Series (inner tags):"
msgstr ""

#: orgSeries-options.php:952
msgid "Latest Series (tags after):"
msgstr ""

#: orgSeries-options.php:942
msgid "Latest Series (tags before):"
msgstr ""

#: orgSeries-options.php:687
msgid "Layout:"
msgstr ""

#: orgSeries-setup.php:297 orgSeries-setup.php:298
msgctxt "leave the %tokens% as is when translating"
msgid ""
"This entry is part %series_part% of %total_posts_in_series% in the series "
msgstr ""

#: src/views/admin/templates/license_key_form.template.php:16
#, php-format
msgid "License Key for %1$s"
msgstr ""

#: src/domain/services/admin/LicenseKeyFormManager.php:241
msgid "License Key successfully activated."
msgstr ""

#: src/domain/services/admin/LicenseKeyFormManager.php:242
msgid "License Key successfully deactivated."
msgstr ""

#: orgSeries-options.php:884
msgid "Limit series meta to single page only"
msgstr ""

#: orgSeries-options.php:636
msgid "List"
msgstr ""

#: orgSeries-widgets.php:281
msgid "List:"
msgstr ""

#: orgSeries-admin.php:535
msgid "Manage All Series"
msgstr ""

#: orgSeries-options.php:87 addons/publisher/series_im_admin_main.php:2
#: addons/publisher/series_issue_manager.php:500
msgid "Manage Series"
msgstr ""

#: addons/publisher/series_issue_manager.php:499
msgid "Manage Series Issues"
msgstr ""

#: orgSeries-options.php:653
msgid "Maximum number of items in Series Post List"
msgstr ""

#: orgSeries-options.php:272
msgid "Metabox"
msgstr ""

#: orgSeries-options.php:1090
msgid "Metabox Series Order"
msgstr ""

#: orgSeries-options.php:1051
msgid "Migrate"
msgstr ""

#: orgSeries-options.php:1055
msgid "Migrate series to new taxonomy"
msgstr ""

#: orgSeries-widgets.php:20
msgid "Most Recent Series"
msgstr ""

#: addons/publisher/series-publish-post-table.php:435
#: addons/publisher/series-pending-post-table.php:314
#: addons/publisher/series-part-post-table.php:320
msgid "Move to Trash"
msgstr ""

#: orgSeries-widgets.php:299
msgid "name"
msgstr ""

#: orgSeries-widgets.php:88
msgid "Name of Series"
msgstr ""

#: addons/grouping/legacy-includes.php:224
msgid "New Series Category"
msgstr ""

#: orgSeries-options.php:86 orgSeries-setup.php:249
msgid "New Series Name"
msgstr ""

#: orgSeries-options.php:918
msgid "Next Post"
msgstr ""

#: orgSeries-options.php:573
msgid "No"
msgstr ""

#: orgSeries-manage.php:257
msgid "No icon currently"
msgstr ""

#: addons/publisher/series-pending-post-table.php:303
msgid "No Part Number"
msgstr ""

#: orgSeries-admin.php:499 orgSeries-setup.php:254
msgid "No Series"
msgstr ""

#: orgSeries-options.php:88 orgSeries-setup.php:251
msgid "No series found"
msgstr ""

#: src/domain/exceptions/NonceFailException.php:18
msgid "Nonce fail."
msgstr ""

#: orgSeries-manage.php:316
msgid "Not enough permission"
msgstr ""

#: orgSeries-admin.php:344
msgid "Not part of a series"
msgstr ""

#: addons/publisher/series_issue_manager.php:528
msgid "Number of items per page"
msgstr ""

#: orgSeries-widgets.php:87
msgid "Number of posts in Series"
msgstr ""

#: orgSeries-widgets.php:95
msgid "Number of series to display:"
msgstr ""

#: orgSeries-widgets.php:363
msgid "Number of Series:"
msgstr ""

#: orgSeries-widgets.php:368
msgid "Offset:"
msgstr ""

#: orgSeries-manage.php:52 orgSeries-manage.php:206 orgSeries-manage.php:238
msgid "Order"
msgstr ""

#: orgSeries-options.php:725
msgid "Order by date"
msgstr ""

#: orgSeries-widgets.php:295
msgid "Order by:"
msgstr ""

#: orgSeries-options.php:722
msgid "Order series by:"
msgstr ""

#: orgSeries-widgets.php:315
msgid "Order:"
msgstr ""

#: orgSeries-widgets.php:234
msgid "Other posts in series:"
msgstr ""

#: orgSeries-options.php:328
msgid "Overview"
msgstr ""

#: orgSeries-taxonomy.php:659
msgid "Part:"
msgstr ""

#: orgSeries-options.php:110
msgid "Permission denied"
msgstr ""

#: orgSeries-options.php:608
msgid ""
"Please change these settings carefully as they make significant changes to "
"PublishPress Series."
msgstr ""

#: orgSeries-options.php:1180 orgSeries-options.php:1202
msgid "Please update your PublishPress Series data"
msgstr ""

#: orgSeries-options.php:1057
msgid ""
"Please use with caution. Running this process will delete all the terms from "
"the current taxonomy and migrate them to a new taxonomy."
msgstr ""

#: orgSeries-options.php:81 orgSeries-setup.php:244
msgid "Popular Series"
msgstr ""

#: addons/grouping/legacy-includes.php:219
msgid "Popular Series Categories"
msgstr ""

#: addons/publisher/series_issue_manager.php:316
msgid "Post moved to the Trash."
msgstr ""

#: orgSeries-admin.php:428
msgid "Post title in widget:"
msgstr ""

#: orgSeries-setup.php:717
msgid "Posts from the series: "
msgstr ""

#: addons/publisher/series-publish-post-table.php:77
#: addons/publisher/series-publish-post-table.php:423
msgid "Preview"
msgstr ""

#: orgSeries-options.php:923
msgid "Previous Post"
msgstr ""

#: addons/publisher/series_issue_manager.php:709
msgid "Publication Date/Time:"
msgstr ""

#: addons/publisher/series_im_admin_main.php:22
msgid "Publish or schedule posts"
msgstr ""

#: addons/publisher/series_im_admin_main.php:9
#: addons/publisher/series_issue_manager.php:695
#: addons/publisher/series_issue_manager.php:741
msgid "Publish Series"
msgstr ""

#: addons/publisher/series_issue_manager.php:915
msgid "Publish Unpublished Posts"
msgstr ""

#: addons/publisher/series_issue_manager.php:643
msgid "Publishing Series:"
msgstr ""

#: orgSeries-admin.php:172
msgid "PublishPress Series Documentation"
msgstr ""

#: orgSeries-options.php:44
msgid "PublishPress Series Options"
msgstr ""

#: orgSeries-options.php:285
msgid "PublishPress Series Plugin Options"
msgstr ""

#: orgSeries-options.php:117
msgid "PublishPress Series Plugin Options have been RESET"
msgstr ""

#: orgSeries-options.php:152
msgid "PublishPress Series Plugin Options have been updated"
msgstr ""

#: orgSeries-widgets.php:91
msgid "Random"
msgstr ""

#: orgSeries-options.php:1161
msgid "Reset options to default"
msgstr ""

#: orgSeries-options.php:1158
msgid "Reset settings"
msgstr ""

#: orgSeries-options.php:1145
msgid "Run Upgrade Task"
msgstr ""

#: orgSeries-options.php:342
#, php-format
msgid ""
"Same as %series_icon% except that the series icon will be linked to the "
"series page"
msgstr ""

#: orgSeries-options.php:366
#, php-format
msgid ""
"Same as %series_title% except that it will also be linked to the series page"
msgstr ""

#: addons/publisher/series_issue_manager.php:664
msgid "Search"
msgstr ""

#. %s: search keywords
#: addons/publisher/series_issue_manager.php:649
#, php-format
msgid "Search results for &#8220;%s&#8221;"
msgstr ""

#: orgSeries-options.php:80 orgSeries-setup.php:243
msgid "Search Series"
msgstr ""

#: orgSeries-admin.php:406
msgid "Search series"
msgstr ""

#: addons/grouping/legacy-includes.php:218
msgid "Search Series Categories"
msgstr ""

#: orgSeries-widgets.php:374
msgid "Search:"
msgstr ""

#: orgSeries-setup.php:192
msgid "Select \"Not part of a series\" to remove any series data from post"
msgstr ""

#: orgSeries-manage.php:217 orgSeries-manage.php:275
msgid "Select Image"
msgstr ""

#: orgSeries-widgets.php:178
msgid "Select Series"
msgstr ""

#: orgSeries-widgets.php:233 orgSeries-widgets.php:386
#: orgSeries-taxonomy.php:374 orgSeries-options.php:45 orgSeries-admin.php:446
#: orgSeries-admin.php:459 orgSeries-admin.php:545 orgSeries-setup.php:250
#: addons/publisher/series_im_admin_main.php:7
#: addons/grouping/legacy-includes.php:396
msgid "Series"
msgid_plural "Series"
msgstr[0] ""
msgstr[1] ""

#: addons/grouping/legacy-includes.php:225
#: addons/grouping/legacy-includes.php:275
#: addons/grouping/legacy-includes.php:275
#: addons/grouping/legacy-includes.php:347
#: addons/grouping/legacy-includes.php:441
#: addons/grouping/legacy-includes.php:482
#: addons/grouping/legacy-includes.php:537
msgid "Series Categories"
msgstr ""

#: orgSeries-manage.php:272
msgid "Series Icon Upload:"
msgstr ""

#: orgSeries-widgets.php:302
msgid "series id"
msgstr ""

#: orgSeries-setup.php:256
msgid "Series Link"
msgstr ""

#: orgSeries-setup.php:255
msgid "Series List"
msgstr ""

#: orgSeries-options.php:875
msgid "Series Meta (with excerpts):"
msgstr ""

#: orgSeries-options.php:848
msgid "Series Meta Box"
msgstr ""

#: orgSeries-options.php:854
msgid "Series Meta:"
msgstr ""

#: orgSeries-options.php:860
msgid "Series Metabox Location"
msgstr ""

#: orgSeries-setup.php:303
msgid "Series Navigation"
msgstr ""

#: orgSeries-options.php:892
msgid "Series Navigation Box"
msgstr ""

#: orgSeries-manage.php:183 orgSeries-manage.php:192 orgSeries-manage.php:192
#: orgSeries-admin.php:354 addons/publisher/series_im_admin_main.php:8
#: addons/publisher/series_issue_manager.php:751
#: addons/publisher/series_issue_manager.php:826
msgid "Series Order"
msgstr ""

#: addons/publisher/series_im_admin_main.php:19
msgid "Series order"
msgstr ""

#: orgSeries-options.php:729
msgid "Series order method"
msgstr ""

#: addons/publisher/series_issue_manager.php:792
msgid "Series Order:"
msgstr ""

#: orgSeries-options.php:724
msgid "Series part"
msgstr ""

#: orgSeries-admin.php:421
msgid "Series Part:"
msgstr ""

#: orgSeries-options.php:773
msgid "Series Per Page:"
msgstr ""

#: orgSeries-options.php:814
msgid "Series Post List"
msgstr ""

#: orgSeries-options.php:809
msgid "Series Post List Box"
msgstr ""

#: orgSeries-options.php:819
msgid "Series Post List box Location"
msgstr ""

#: orgSeries-options.php:840
msgid "Series Post List Post Title (Current Post)"
msgstr ""

#: orgSeries-options.php:834
msgid "Series Post List Post Title (Linked Post)"
msgstr ""

#: orgSeries-options.php:903
msgid "Series Post Navigation Location"
msgstr ""

#: orgSeries-options.php:898
msgid "Series Post Navigation:"
msgstr ""

#: orgSeries-options.php:1127
msgid "Series Settings"
msgstr ""

#: orgSeries-widgets.php:89
msgid "Series Slug"
msgstr ""

#: orgSeries-widgets.php:112 orgSeries-options.php:960
#: orgSeries-options.php:966 orgSeries-setup.php:421
msgid "Series Table of Contents"
msgstr ""

#: orgSeries-options.php:777
msgid "Series Table of Contents Title:"
msgstr ""

#: orgSeries-options.php:739
msgid "Series Table of Contents URL:"
msgstr ""

#: orgSeries-options.php:1032
msgid "Series Taxonomy Slug:"
msgstr ""

#: orgSeries-options.php:1040
msgid "Series Taxonomy:"
msgstr ""

#: orgSeries-options.php:1140
msgid "Series Upgrade"
msgstr ""

#: orgSeries-options.php:1250
msgid "Series upgrade completed."
msgstr ""

#: orgSeries-widgets.php:416
msgid "Series widget title:"
msgstr ""

#: orgSeries-taxonomy.php:657 orgSeries-taxonomy.php:686
msgid "Series:"
msgstr ""

#: orgSeries-setup.php:715
msgid "Series: "
msgstr ""

#: orgSeries-manage.php:210
msgid ""
"Set a specific order by entering a number (1 for first, etc.) in this field."
msgstr ""

#: orgSeries-options.php:33 orgSeries-options.php:54 orgSeries-options.php:55
#: orgSeries-setup.php:729
msgid "Settings"
msgstr ""

#: orgSeries-options.php:1082
msgid "Show \"Add New\""
msgstr ""

#: orgSeries-options.php:1086
msgid "Show \"Post title in widget\""
msgstr ""

#: orgSeries-widgets.php:380
msgid "Show other posts in the current series:"
msgstr ""

#: orgSeries-widgets.php:285
msgid "Show post count:"
msgstr ""

#: orgSeries-widgets.php:274
msgid "Show Series Table Of Content"
msgstr ""

#: orgSeries-widgets.php:279
msgid "Show Series Table Of Content as:"
msgstr ""

#: orgSeries-widgets.php:301
msgid "slug"
msgstr ""

#: orgSeries-admin.php:204
msgid "Sorry but you don't have permission to add series"
msgstr ""

#: inc/templates/taxonomy-series.php:70
msgid "Sorry, no results found."
msgstr ""

#: addons/publisher/series-publish-post-table.php:76
#: addons/publisher/series-pending-post-table.php:69
#: addons/publisher/series-part-post-table.php:66
msgid "Status"
msgstr ""

#: orgSeries-options.php:669
msgid "Style options"
msgstr ""

#: addons/publisher/series-publish-post-table.php:75
#: addons/publisher/series-pending-post-table.php:68
#: addons/publisher/series-part-post-table.php:65
msgid "Tags"
msgstr ""

#: orgSeries-options.php:271
msgid "Taxonomy"
msgstr ""

#: orgSeries-options.php:78 orgSeries-setup.php:241
msgctxt "taxonomy general name"
msgid "Series"
msgstr ""

#: addons/grouping/legacy-includes.php:216
msgctxt "taxonomy general name"
msgid "Series Categories"
msgstr ""

#: orgSeries-options.php:79 orgSeries-setup.php:242
msgctxt "taxonomy singular name"
msgid "Series"
msgstr ""

#: addons/grouping/legacy-includes.php:217
msgctxt "taxonomy singular name"
msgid "Series Category"
msgstr ""

#: orgSeries-utility.php:310
msgid "Templates"
msgstr ""

#: orgSeries-manage.php:67
msgid "Term Order"
msgstr ""

#: orgSeries-manage.php:244
msgid ""
"Terms are usually ordered alphabetically, but you can choose your own order "
"by entering a number (1 for first, etc.) in this field."
msgstr ""

#: src/domain/exceptions/InvalidFilePathException.php:27
#, php-format
msgid ""
"The \"%1$s\" file is either missing or could not be read due to permissions. "
"Please ensure that the following path is correct and verify that the file "
"permissions are correct:%2$s %3$s"
msgstr ""

#: src/application/Container.php:160
#, php-format
msgid "The %1$s already has a parameter indexed with the name: %2$s."
msgstr ""

#: src/application/Root.php:139
#, php-format
msgid "The %1$s method can only be used to register a child of %2$s."
msgstr ""

#: orgSeries-options.php:511
msgid "The date that a post was published"
msgstr ""

#: orgSeries-options.php:330
msgid ""
"The following is a legend of the tokens that are available for use in the "
"custom template fields. These will be replaced with the appropriate values "
"when the plugin runs."
msgstr ""

#: src/domain/model/FileLocation.php:50
#, php-format
msgid "The given file path (%s) is not readable."
msgstr ""

#: src/domain/model/RouteIdentifier.php:51
#, php-format
msgid "The incoming value for %1$s is expected to be a closure.  It was not."
msgstr ""

#: orgSeries-widgets.php:79
#, php-format
msgid ""
"The layout and content of this widget can be adjusted via the <a href=\"%s\">"
"Latest Series</a> area."
msgstr ""

#: orgSeries-admin.php:215
msgid ""
"The name you picked isn't sanitizing correctly. Try something different."
msgstr ""

#: src/domain/services/AssetRegistry.php:408
#, php-format
msgid ""
"The namespace for this manifest file has already been registered, choose a "
"namespace other than %s"
msgstr ""

#: addons/publisher/series_issue_manager.php:290
msgid "The posts in your series were successfully unpublished."
msgstr ""

#: src/domain/model/ControllerRoute.php:50
#: src/domain/model/HasHooksRoute.php:59
#, php-format
msgid ""
"The provided object fully qualified class name (%1$s) must implement the "
"%2$s interface."
msgstr ""

#: src/domain/services/AssetRegistry.php:419
#, php-format
msgid ""
"The provided value for %1$s is not a valid url.  The url provided was: %2$s"
msgstr ""

#: src/domain/services/AssetRegistry.php:283
#, php-format
msgid ""
"The value for %1$s already exists in the %2$s property. Overrides are not "
"allowed.  Consider attaching your value to a different key"
msgstr ""

#: src/domain/services/AssetRegistry.php:271
#, php-format
msgid ""
"The value for %1$s already exists in the %2$s property. Overrides are not "
"allowed. Since the value of this data is an array, you may want to use the "
"%3$s method to push your value to the array."
msgstr ""

#: src/domain/services/AssetRegistry.php:228
#, php-format
msgid ""
"The value for %1$s is already set and it is not an array. The %2$s method "
"can only be used to push values to this data element when it is an array."
msgstr ""

#: addons/publisher/series-part-post-table.php:185
msgid "There are no published posts in this series."
msgstr ""

#: addons/publisher/series-publish-post-table.php:46
#: addons/publisher/series-pending-post-table.php:188
msgid "There are no unpublished posts in this series."
msgstr ""

#: orgSeries-options.php:587
msgid ""
"These settings allow you to customize the main frontend screens in "
"PublishPress Series."
msgstr ""

#: orgSeries-options.php:622
msgid ""
"These settings allow you to customize the metabox on the post editing screen."
msgstr ""

#: orgSeries-options.php:594
msgid ""
"These templates allow you to customize the frontend appearance of "
"PublishPress Series."
msgstr ""

#: orgSeries-options.php:879
msgid ""
"This control how and what series meta information is displayed with posts "
"that are part of a series when the_excerpt is used. "
msgstr ""

#: orgSeries-options.php:894
msgid "This display is shown at the bottom of all posts in a series."
msgstr ""

#: orgSeries-options.php:811 orgSeries-options.php:850
msgid "This display is shown at the top of all posts in a series."
msgstr ""

#: orgSeries-options.php:938
msgid "This display is used by the \"Latest Series\" widget."
msgstr ""

#: orgSeries-options.php:962
msgid ""
"This display is used by the \"Series Table of Contents\" widget, shortcode, "
"and URL."
msgstr ""

#: orgSeries-options.php:969
msgid ""
"This display is used by the \"Series Table of Contents\". To find the URL "
"for this display, go the \"Display\" tab and then \"Series Table of Contents "
"URL\"."
msgstr ""

#: orgSeries-options.php:615
msgid ""
"This feature allows you to create a new taxonomy for this plugin to use if "
"you don't want to use the default \"Series\" taxonomy."
msgstr ""

#: orgSeries-options.php:601
msgid ""
"This section is for the icons that show with your series. Note that you must "
"use a token for the icon in the \"Templates\" settings."
msgstr ""

#: orgSeries-options.php:1035
msgid "This text will be part of the series base URL."
msgstr ""

#: orgSeries-widgets.php:418
msgid "This text will display above other posts in this series."
msgstr ""

#: orgSeries-options.php:350
msgid ""
"This token is for use with the orgSeries widget only - it references where "
"you want the list of series titles to be inserted and requires that the "
"template for each series title be also set."
msgstr ""

#: orgSeries-options.php:334
msgid "This will be replaced with the series icon for a series."
msgstr ""

#: orgSeries-options.php:358
msgid "This will be replaced with the title of a series"
msgstr ""

#: addons/publisher/series-publish-post-table.php:72
#: addons/publisher/series-pending-post-table.php:65
#: addons/publisher/series-part-post-table.php:62
msgid "Title"
msgstr ""

#: orgSeries-widgets.php:73 orgSeries-widgets.php:269
msgid "Title:"
msgstr ""

#: orgSeries-options.php:1044
msgid ""
"To create a new taxonomy, enter the new name and click the \"Update "
"Options\" button."
msgstr ""

#: orgSeries-manage.php:68
msgid ""
"To reposition an item, drag and drop the row by \"clicking and holding\" it "
"anywhere and moving it to its new position."
msgstr ""

#: orgSeries-options.php:797
msgid "Top"
msgstr ""

#: addons/publisher/series-publish-post-table.php:288
#: addons/publisher/series-pending-post-table.php:161
#: addons/publisher/series-part-post-table.php:158
msgid "Trash"
msgstr ""

#: src/domain/exceptions/EntityNotFoundException.php:30
#, php-format
msgid "Unable to retrieve an instance of %1$s. Not found."
msgstr ""

#: addons/publisher/series_issue_manager.php:901
msgid "Unpublished Posts"
msgstr ""

#: addons/publisher/series_issue_manager.php:867
msgid "Unpublished posts in series:"
msgstr ""

#: addons/publisher/series_issue_manager.php:925
msgid "Unpublished Series Order"
msgstr ""

#: orgSeries-options.php:568
msgid "Update Options"
msgstr ""

#: addons/publisher/series_issue_manager.php:765
#: addons/publisher/series_issue_manager.php:840
#: addons/publisher/series_issue_manager.php:939
msgid "Update Order"
msgstr ""

#: orgSeries-options.php:84 orgSeries-setup.php:247
msgid "Update Series"
msgstr ""

#: addons/grouping/legacy-includes.php:222
msgid "Update Series Category"
msgstr ""

#: orgSeries-options.php:1183 orgSeries-options.php:1204
msgid "Update Series data"
msgstr ""

#: orgSeries-manage.php:320
msgid "Updated successfully"
msgstr ""

#: orgSeries-manage.php:219 orgSeries-manage.php:276
msgid "Upload an image for the series."
msgstr ""

#: orgSeries-options.php:672
msgid "Use box style"
msgstr ""

#: orgSeries-options.php:673
msgid "Use dark style"
msgstr ""

#: orgSeries-options.php:671
msgid "Use default style"
msgstr ""

#: orgSeries-options.php:674
msgid "Use light style"
msgstr ""

#: orgSeries-options.php:665
msgid "Use PublishPress Series CSS styles?"
msgstr ""

#: orgSeries-options.php:445
msgid ""
"Use this tag either before or after the rest of the template code.  It will "
"indicate where you want the content of a post to display."
msgstr ""

#: orgSeries-widgets.php:12
msgid "Use this to control the output of the latest series widget"
msgstr ""

#: orgSeries-widgets.php:111
msgid "Use this to display the Series Table of contents"
msgstr ""

#: orgSeries-admin.php:526
msgid "View all series"
msgstr ""

#: orgSeries-options.php:742
msgid "view page"
msgstr ""

#: orgSeries-setup.php:253
msgid "View Series"
msgstr ""

#: addons/publisher/series_im_admin_main.php:25
msgid "View series in frontend"
msgstr ""

#: orgSeries-options.php:1182 orgSeries-options.php:1203
msgid ""
"We have made changes to how series are stored and this requires a small "
"update. This improves support for the Multiple Series feature, and resolves "
"some issues with the order of posts. Please click this button to upgrade."
msgstr ""

#: addons/publisher/series_issue_manager.php:212
msgid ""
"When checked, all posts you assign to this series will remain unpublished "
"until you publish the entire series."
msgstr ""

#: orgSeries-widgets.php:90
msgid "When Series was Created"
msgstr ""

#: addons/publisher/series_issue_manager.php:870
msgid ""
"When these posts are published, they will be added after the last current "
"post in the series."
msgstr ""

#: orgSeries-options.php:886
msgid ""
"Whether to limit series meta display to single page only or include archive "
"page."
msgstr ""

#: orgSeries-options.php:1008
msgid "Width for icon if displayed via the latest series template (in pixels)."
msgstr ""

#: orgSeries-options.php:1002
msgid "Width for icon on a post page (in pixels)."
msgstr ""

#: orgSeries-options.php:996
msgid "Width for icon on series table of contents page (in pixels)"
msgstr ""

#: orgSeries-options.php:438
msgid ""
"Will be replaced by the navigation link for the first post in a series. The "
"text will be whatever is included in the 'Custom First Post Navigation Text' "
"field. If that field is empty then the text will be the title of the post"
msgstr ""

#: orgSeries-options.php:430
msgid ""
"Will be replaced by the navigation link for the next post in a series. The "
"text will be whatever is included in the 'Custom Next Post Navigation Text' "
"field. If that field is empty then the text will be the title of the post"
msgstr ""

#: orgSeries-options.php:422
msgid ""
"Will be replaced by the navigation link for the previous post in a series. "
"The text will be whatever is included in the 'Custom Previous Post "
"Navigation Text' field. If that field is empty then the text will be the "
"title of the post"
msgstr ""

#: orgSeries-options.php:390
msgid "Will be replaced with the post title of a post in the series"
msgstr ""

#: orgSeries-options.php:398
msgid ""
"Will be replaced with the post title of a post in the series linked to the "
"page view of that post."
msgstr ""

#: orgSeries-options.php:406
msgid "Will be replaced with the post title short of a post in the series"
msgstr ""

#: orgSeries-options.php:414
msgid ""
"Will be replaced with the post title short of a post in the series linked to "
"the page view of that post."
msgstr ""

#: orgSeries-options.php:519
msgid ""
"Will be replaced with the unpublished post title of a post in the series"
msgstr ""

#: orgSeries-options.php:469
msgid "Will display the description for the series"
msgstr ""

#: orgSeries-options.php:461
msgid "Will display the total number of posts in a series"
msgstr ""

#: orgSeries-options.php:527
msgid ""
"Will display the total number of published and unpublished posts in a series"
msgstr ""

#: orgSeries-options.php:453
msgid "Will display what part of a series the post is"
msgstr ""

#: orgSeries-options.php:495
msgid "Will output the author of the post"
msgstr ""

#: orgSeries-options.php:487
msgid "Will output the ID of the series"
msgstr ""

#: orgSeries-options.php:479
msgid "Will output the slug of the series"
msgstr ""

#: orgSeries-options.php:574
msgid "Yes"
msgstr ""

#: orgSeries-widgets.php:376
msgid ""
"You can return any series that match this search string (matched against "
"series names)"
msgstr ""

#: orgSeries-widgets.php:370
msgid ""
"You can select the offset for the number of series (useful for paging).  No "
"offset if left blank"
msgstr ""

#: addons/publisher/series_im_admin_main.php:34
msgid "You have no series available."
msgstr ""

#: orgSeries-options.php:752
#, php-format
msgid ""
"You must %1s update your permalink structure %2s to something other than "
"\"Plain\" for the Series Table of Contents URL to work."
msgstr ""
