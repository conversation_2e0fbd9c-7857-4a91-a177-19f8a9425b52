msgid ""
msgstr ""
"Project-Id-Version: PublishPress Series Pro\n"
"POT-Creation-Date: 2024-11-19 02:35-0300\n"
"PO-Revision-Date: 2024-12-18 01:08-0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: publishpress-series-pro.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#: addons/grouping/legacy-includes.php:216
msgctxt "taxonomy general name"
msgid "Series Categories"
msgstr "Categorias de series"

#: addons/grouping/legacy-includes.php:217
msgctxt "taxonomy singular name"
msgid "Series Category"
msgstr "Categoria da serie"

#: addons/grouping/legacy-includes.php:218
msgid "Search Series Categories"
msgstr "Pesquisar categorias de séries"

#: addons/grouping/legacy-includes.php:219
msgid "Popular Series Categories"
msgstr "Categorias populares de séries"

#: addons/grouping/legacy-includes.php:220
#: addons/grouping/legacy-includes.php:512
msgid "All Series Categories"
msgstr "Todas as categorias de séries"

#: addons/grouping/legacy-includes.php:221
msgid "Edit Series Category"
msgstr "Editar categoria da série"

#: addons/grouping/legacy-includes.php:222
msgid "Update Series Category"
msgstr "Atualizar categoria da série"

#: addons/grouping/legacy-includes.php:223
msgid "Add New Series Category"
msgstr "Adicionar nova categoria da série"

#: addons/grouping/legacy-includes.php:224
msgid "New Series Category"
msgstr "Nova categoria da série"

#: addons/grouping/legacy-includes.php:225
#: addons/grouping/legacy-includes.php:347
#: addons/grouping/legacy-includes.php:441
#: addons/grouping/legacy-includes.php:482
#: addons/grouping/legacy-includes.php:503
#: addons/grouping/legacy-includes.php:537
msgid "Series Categories"
msgstr "Categorias de series"

#: addons/grouping/legacy-includes.php:353
msgid ""
"Delete all \"Series Categories\" data from the database when deleting the "
"plugin."
msgstr ""
"Exclua todos os dados de \"Categorias de séries\" do banco de dados ao "
"excluir o plugin."

#: addons/grouping/legacy-includes.php:396
#: addons/publisher/series_im_admin_main.php:7
#: includes-pro/addons/multiples/os-multi-setup.php:264
#: includes-pro/addons/multiples/os-multi-setup.php:292 orgSeries-admin.php:446
#: orgSeries-admin.php:459 orgSeries-admin.php:533 orgSeries-options.php:42
#: orgSeries-setup.php:250 orgSeries-taxonomy.php:374 orgSeries-widgets.php:233
#: orgSeries-widgets.php:386
msgid "Series"
msgid_plural "Series"
msgstr[0] "Série"
msgstr[1] "Séries"

#: addons/grouping/legacy-includes.php:482
msgid "Click to toggle"
msgstr "Clique para alternar"

#: addons/grouping/legacy-includes.php:538
msgid "[more]"
msgstr "[mais]"

#: addons/grouping/legacy-includes.php:539
msgid "[less]"
msgstr "[menos]"

#: addons/publisher/series-part-post-table.php:62
#: addons/publisher/series-pending-post-table.php:65
#: addons/publisher/series-publish-post-table.php:72
msgid "Title"
msgstr "Título"

#: addons/publisher/series-part-post-table.php:63
#: addons/publisher/series-pending-post-table.php:66
#: addons/publisher/series-publish-post-table.php:73
msgid "Author"
msgstr "Autor"

#: addons/publisher/series-part-post-table.php:64
#: addons/publisher/series-pending-post-table.php:67
#: addons/publisher/series-publish-post-table.php:74
msgid "Categories"
msgstr "Categorias"

#: addons/publisher/series-part-post-table.php:65
#: addons/publisher/series-pending-post-table.php:68
#: addons/publisher/series-publish-post-table.php:75
msgid "Tags"
msgstr "Tags"

#: addons/publisher/series-part-post-table.php:66
#: addons/publisher/series-pending-post-table.php:69
#: addons/publisher/series-publish-post-table.php:76
#: includes-pro/classes/licence.php:100
msgid "Status"
msgstr "Status"

#: addons/publisher/series-part-post-table.php:67
#: addons/publisher/series-pending-post-table.php:70
msgid "Current Part"
msgstr "Parte atual"

#: addons/publisher/series-part-post-table.php:141
#: addons/publisher/series-pending-post-table.php:144
#: addons/publisher/series-publish-post-table.php:271
msgid "Edit"
msgstr "Editar"

#: addons/publisher/series-part-post-table.php:158
#: addons/publisher/series-pending-post-table.php:161
#: addons/publisher/series-publish-post-table.php:288
msgid "Trash"
msgstr "Lixeira"

#: addons/publisher/series-part-post-table.php:185
msgid "There are no published posts in this series."
msgstr "Não há posts publicados nesta série."

#: addons/publisher/series-part-post-table.php:304
msgid "(Currently has no Part number)"
msgstr "(no momento não tem número da parte)"

#: addons/publisher/series-part-post-table.php:320
#: addons/publisher/series-pending-post-table.php:314
#: addons/publisher/series-publish-post-table.php:435
msgid "Move to Trash"
msgstr "Mover para a lixeira"

#: addons/publisher/series-pending-post-table.php:188
#: addons/publisher/series-publish-post-table.php:46
msgid "There are no unpublished posts in this series."
msgstr "Não há posts não publicados nesta série."

#: addons/publisher/series-pending-post-table.php:303
msgid "No Part Number"
msgstr "Sem número da parte"

#: addons/publisher/series-publish-post-table.php:77
#: addons/publisher/series-publish-post-table.php:423
msgid "Preview"
msgstr "Pré-visualizar"

#: addons/publisher/series-publish-post-table.php:177
msgid "All Categories"
msgstr "Todas as categorias"

#: addons/publisher/series-publish-post-table.php:190
msgid "Filter"
msgstr "Filtrar"

#: addons/publisher/series_im_admin_main.php:2
#: addons/publisher/series_issue_manager.php:500 orgSeries-options.php:84
msgid "Manage Series"
msgstr "Gerenciar séries"

#: addons/publisher/series_im_admin_main.php:8
#: addons/publisher/series_issue_manager.php:751
#: addons/publisher/series_issue_manager.php:826 orgSeries-admin.php:354
#: orgSeries-manage.php:183 orgSeries-manage.php:192
msgid "Series Order"
msgstr "Ordem da série"

#: addons/publisher/series_im_admin_main.php:9
#: addons/publisher/series_issue_manager.php:695
#: addons/publisher/series_issue_manager.php:741
msgid "Publish Series"
msgstr "Publicar séries"

#: addons/publisher/series_im_admin_main.php:10
msgid "Front View"
msgstr "Visualização na interface"

#: addons/publisher/series_im_admin_main.php:17
#, php-format
msgid "Edit the status of %1$s"
msgstr "Editar o status de %1$s"

#: addons/publisher/series_im_admin_main.php:19
msgid "Series order"
msgstr "Ordem da série"

#: addons/publisher/series_im_admin_main.php:22
msgid "Publish or schedule posts"
msgstr "Publicar ou agendar posts"

#: addons/publisher/series_im_admin_main.php:25
msgid "View series in frontend"
msgstr "Ver séries na interface"

#: addons/publisher/series_im_admin_main.php:34
msgid "You have no series available."
msgstr "Você não tem nenhuma série disponível."

#: addons/publisher/series_im_admin_main.php:34
msgid "Add new Series."
msgstr "Adicionar nova série."

#: addons/publisher/series_issue_manager.php:209
msgid "Create as unpublished:"
msgstr "Criar como não publicado:"

#: addons/publisher/series_issue_manager.php:212
msgid ""
"When checked, all posts you assign to this series will remain unpublished "
"until you publish the entire series."
msgstr ""
"Quando estiver marcado, todos os posts que você atribuir a esta série "
"permanecerão não publicados até que você publique a série inteira."

#: addons/publisher/series_issue_manager.php:290
msgid "The posts in your series were successfully unpublished."
msgstr "A publicação dos posts da sua série foi cancelada."

#: addons/publisher/series_issue_manager.php:299
#: addons/publisher/series_issue_manager.php:416
msgid "Congratulations. Your series order was updated successfully."
msgstr "Parabéns! A ordem da sua série foi atualizada."

#: addons/publisher/series_issue_manager.php:307
#: addons/publisher/series_issue_manager.php:344
msgid "Congratulations. Your posts were published successfully."
msgstr "Parabéns! Seus posts foram publicados."

#: addons/publisher/series_issue_manager.php:316
msgid "Post moved to the Trash."
msgstr "Post movido para a lixeira."

#: addons/publisher/series_issue_manager.php:339
msgid "Congratulations. Your posts were scheduled successfully."
msgstr "Parabéns! Seus posts foram agendados."

#: addons/publisher/series_issue_manager.php:499
msgid "Manage Series Issues"
msgstr "Gerenciar problemas da série"

#: addons/publisher/series_issue_manager.php:528
msgid "Number of items per page"
msgstr "Número de itens por página"

#: addons/publisher/series_issue_manager.php:643
msgid "Publishing Series:"
msgstr "Série de publicações:"

#: addons/publisher/series_issue_manager.php:650
#, php-format
msgid "Search results for &#8220;%s&#8221;"
msgstr "Resultados da pesquisa para &#8220;%s&#8221;"

#: addons/publisher/series_issue_manager.php:664
msgid "Search"
msgstr "Pesquisar"

#: addons/publisher/series_issue_manager.php:685
#: addons/publisher/series_issue_manager.php:818
#: addons/publisher/series_issue_manager.php:893
msgid "Description here."
msgstr "Descrição aqui."

#: addons/publisher/series_issue_manager.php:709
msgid "Publication Date/Time:"
msgstr "Data e hora da publicação:"

#: addons/publisher/series_issue_manager.php:733
#, php-format
msgid "%1$s%2$s, %3$s @ %4$s : %5$s"
msgstr "%1$s%2$s, %3$s @ %4$s : %5$s"

#: addons/publisher/series_issue_manager.php:765
#: addons/publisher/series_issue_manager.php:840
#: addons/publisher/series_issue_manager.php:939
msgid "Update Order"
msgstr "Atualizar ordem"

#: addons/publisher/series_issue_manager.php:792
msgid "Series Order:"
msgstr "Ordem da série:"

#: addons/publisher/series_issue_manager.php:795
msgid ""
"Drag the post names into the order you want them to be in the series, from "
"the first part to the last part."
msgstr ""
"Arraste os nomes dos posts para a ordem em que você quer que eles fiquem na "
"série, da primeira para a última parte."

#: addons/publisher/series_issue_manager.php:867
msgid "Unpublished posts in series:"
msgstr "Posts não publicados na série:"

#: addons/publisher/series_issue_manager.php:870
msgid ""
"When these posts are published, they will be added after the last current "
"post in the series."
msgstr ""
"Quando estes posts forem publicados, eles serão adicionados após o último "
"post atual da série."

#: addons/publisher/series_issue_manager.php:901
msgid "Unpublished Posts"
msgstr "Posts não publicados"

#: addons/publisher/series_issue_manager.php:915
msgid "Publish Unpublished Posts"
msgstr "Publicar posts não publicados"

#: addons/publisher/series_issue_manager.php:925
msgid "Unpublished Series Order"
msgstr "Ordem da série não publicada"

#: assets/dist/osjs.8a010d69758cc52eda2d.dist.js:1
msgid "delete"
msgstr "excluir"

#: assets/dist/osjs.8a010d69758cc52eda2d.dist.js:1
msgid "has"
msgstr "tem"

#: assets/dist/osjs.8a010d69758cc52eda2d.dist.js:1
msgid "get"
msgstr "receber"

#: inc/orgSeries_updates.php:156
msgid "Publishpress Series API: "
msgstr "API do Publishpress Series: "

#: inc/orgSeries_updates.php:272
#, php-format
msgid ""
"<p>There is an automatic update for %s available but your api-key has not "
"been set.  Please go to the <a href=\"admin.php?"
"page=orgseries_options_page\">Publishpress Series Options page</a> to set "
"your api_key for this addon.</p>"
msgstr ""
"<p>Há uma atualização automática disponível para o %s, mas sua chave API não "
"foi definida. Acesse a página <a href=\"admin.php?"
"page=orgseries_options_page\">Opções do Publishpress Series</a> para definir "
"sua chave de API para este complemento.</p>"

#: inc/orgSeries_updates.php:276
#, php-format
msgid ""
"<p>There is an automatic update for %s available but your api-key is "
"invalid.  Either you have entered the wrong key on the <a href=\"admin.php?"
"page=orgseries_options_page\">Publishpress Series Options</a> page OR you no "
"longer have access to the updates for this plugin.  Automatic upgrades for "
"single website install's are gained via the <a href=\"https://publishpress."
"com/pricing\">Basic Support package</a>.  Automatic upgrades for multiple "
"website install's are available via the <a href=\"https://publishpress.com/"
"pricing\">All Addons package or the Lifetime Membership package.</a></p>"
msgstr ""
"<p>Há uma atualização automática disponível para o %s, mas sua chave de API "
"é inválida. Ou você digitou a chave errada na página <a href=\"admin.php?"
"page=orgseries_options_page\">Opções do Publishpress Series</a> OU você não "
"tem mais acesso às atualizações deste plugin. As atualizações automáticas "
"para instalações de um único site são realizadas através do pacote de "
"suporte <a href=\"https://publishpress.com/pricing\">Basic</a>. As "
"atualizações automáticas para instalações de vários sites estão disponíveis "
"através do pacote <a href=\"https://publishpress.com/pricing\">\"All "
"Addons\" ou do pacote \"Lifetime Membership\".</a></p>"

#: inc/orgSeries_updates.php:281
msgid "Dismiss"
msgstr "Dispensar"

#: inc/templates/taxonomy-series.php:70
msgid "Sorry, no results found."
msgstr "Nenhum resultado encontrado."

#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:96
#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:119
msgid "Post Types"
msgstr "Tipos de posts"

#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:98
#: includes-pro/admin.php:60
msgid "Custom Post Type Support"
msgstr "Suporte a tipos de post personalizados"

#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:109
msgid "Enable PublishPress Series for custom post types."
msgstr "Ative o PublishPress Series para tipos de posts personalizados."

#: includes-pro/addons/extra-tokens/legacy-includes.php:81
msgid "Will display the slug for the series"
msgstr "Irá exibir o slug da série"

#: includes-pro/addons/extra-tokens/legacy-includes.php:84
msgid "Will display the series id of the series"
msgstr "Irá exibir o ID da série"

#: includes-pro/addons/extra-tokens/legacy-includes.php:87
msgid "Will display the post author of the post in the series"
msgstr "Irá exibir o autor do post na série"

#: includes-pro/addons/extra-tokens/legacy-includes.php:90
msgid "Will display the post thumbnail of a post belonging to the series"
msgstr "Irá exibir a miniatura do post de um post pertencente à série"

#: includes-pro/addons/extra-tokens/legacy-includes.php:93
msgid "Will display the published date of a post within a series"
msgstr "Irá exibir a data de publicação de um post em uma série"

#: includes-pro/addons/extra-tokens/legacy-includes.php:96
msgid ""
"Will be replaced with the unpublished post title of a post in the series"
msgstr "Será substituído pelo título do post não publicado de um post da série"

#: includes-pro/addons/extra-tokens/legacy-includes.php:99
msgid ""
"Will display the total number of published and unpublished posts in a series"
msgstr ""
"Irá exibir o número total de posts publicados e não publicados em uma série"

#: includes-pro/addons/extra-tokens/legacy-includes.php:125
msgid "Series Post List Post Title(Unpublished)"
msgstr "Título do post da lista de posts da série (não publicado)"

#: includes-pro/addons/multiples/os-multi-setup.php:67 orgSeries-admin.php:398
#: orgSeries-setup.php:191
msgid "Add New"
msgstr "Adicionar nova"

#: includes-pro/addons/multiples/os-multi-setup.php:69
msgid "To remove a post from series, just deselect any checkboxes"
msgstr "Para remover um post da série, basta desmarcar as caixas de seleção"

#: includes-pro/addons/multiples/os-multi-setup.php:91
#: orgSeries-taxonomy.php:657 orgSeries-taxonomy.php:686
msgid "Series:"
msgstr "Séries:"

#: includes-pro/addons/multiples/os-multi-setup.php:224 orgSeries-admin.php:204
msgid "Sorry but you don't have permission to add series"
msgstr "Você não tem permissão para adicionar séries"

#: includes-pro/addons/multiples/os-multi-setup.php:235 orgSeries-admin.php:215
msgid ""
"The name you picked isn't sanitizing correctly. Try something different."
msgstr ""
"O nome que você escolheu não está sendo higienizado corretamente. Tente algo "
"diferente."

#: includes-pro/addons/multiples/os-multi-setup.php:240 orgSeries-admin.php:221
msgid ""
"Hmm... it looks like there is already a series with that name. Try something "
"else"
msgstr "Hummm... parece que já existe uma série com este nome. Tente outro"

#: includes-pro/addons/multiples/os-multi-setup.php:293
msgid "Part"
msgstr "Parte"

#: includes-pro/addons/multiples/os-multi-setup.php:303 orgSeries-admin.php:428
msgid "Post title in widget:"
msgstr "Título do post no widget:"

#: includes-pro/addons/multiples/os-multi-setup.php:305 orgSeries-admin.php:430
msgid ""
"A short title of this post that will be used in the Series widget. Leave "
"blank to use the full title."
msgstr ""
"Um título curto deste post que será usado no widget da série. Deixe em "
"branco para usar o título inteiro."

#: includes-pro/addons/multiples/os-multi-setup.php:336 orgSeries-admin.php:487
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (No part number)"
msgstr "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (sem número da parte)"

#: includes-pro/addons/multiples/os-multi-setup.php:339 orgSeries-admin.php:489
#, php-format
msgid "<a href=\"%3$s\" title=\"%4$s\">%5$s</a> (Part %1$s of %2$s)"
msgstr "<a href=\"%3$s\" title=\"%4$s\">%5$s</a> (parte %1$s de %2$s)"

#: includes-pro/addons/multiples/os-multi-setup.php:343
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (No Part Number)"
msgstr "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (sem número da parte)"

#: includes-pro/addons/multiples/os-multi-setup.php:370 orgSeries-admin.php:499
#: orgSeries-setup.php:254
msgid "No Series"
msgstr "Nenhuma série"

#: includes-pro/admin.php:11 includes-pro/admin.php:31
msgid "Pro Features"
msgstr "Recursos Pro"

#: includes-pro/admin.php:12
msgid "Enable Features"
msgstr "Ativar recursos"

#: includes-pro/admin.php:13 includes-pro/admin.php:32
msgid "License"
msgstr "Licença"

#: includes-pro/admin.php:14
msgid "Licence"
msgstr "Licença"

#: includes-pro/admin.php:40
msgid ""
"These settings allow you enable or disable features in PublishPress Series "
"Pro."
msgstr ""
"Estas configurações permitem que você ative ou desative recursos no "
"PublishPress Series Pro."

#: includes-pro/admin.php:47
msgid "Your PublishPress license key provides access to updates and support."
msgstr ""
"Sua chave de licença do PublishPress fornece acesso a atualizações e suporte."

#: includes-pro/admin.php:60
msgid "Allow custom post types to be used with PublishPress Series."
msgstr ""
"Permite que tipos de post personalizados sejam usados com o PublishPress "
"Series."

#: includes-pro/admin.php:61
msgid "Shortcodes"
msgstr "Shortcodes"

#: includes-pro/admin.php:61
msgid "Provides shortcodes to display series information."
msgstr "Fornece shortcodes para exibir informações de séries."

#: includes-pro/admin.php:62
msgid "Extra Tokens"
msgstr "Tokens adicionais"

#: includes-pro/admin.php:62
msgid "Provides extra tokens to customize the output of series information."
msgstr ""
"Fornece tokens adicionais para personalizar a saída das informações da série."

#: includes-pro/admin.php:63
msgid "Multiple Series"
msgstr "Várias séries"

#: includes-pro/admin.php:63
msgid "Allows authors to add posts to more than one series."
msgstr "Permite que os autores adicionem posts a mais de uma série."

#: includes-pro/classes/licence.php:93
msgid "License key:"
msgstr "Chave de licença:"

#: includes-pro/classes/licence.php:103
msgid "Your license key provides access to updates and support."
msgstr "Sua chave de licença fornece acesso a atualizações e suporte."

#: includes-pro/classes/licence.php:112 includes-pro/classes/licence.php:130
#: includes-pro/classes/licence.php:140
#: src/domain/services/admin/LicenseKeyFormManager.php:123
#: src/views/admin/templates/license_key_form.template.php:33
msgid "Activate License"
msgstr "Ativar licença"

#: includes-pro/classes/licence.php:124
#: src/domain/services/admin/LicenseKeyFormManager.php:122
#: src/views/admin/templates/license_key_form.template.php:31
msgid "Deactivate License"
msgstr "Desativar licença"

#: orgSeries-admin.php:67
msgid "An error occured"
msgstr "Ocorreu um erro"

#: orgSeries-admin.php:70 orgSeries-options.php:145
#, php-format
msgid "%1$s series migrated to new taxonomy"
msgstr "%1$s series migradas para a nova taxonomia"

#: orgSeries-admin.php:160
#, php-format
msgid "If you like %s, please leave us a %s rating. Thank you!"
msgstr ""
"Se você estiver gostando do %s, nos deixe uma classificação de %s. Obrigado!"

#: orgSeries-admin.php:171
msgid "About PublishPress Series"
msgstr "Sobre o PublishPress Series"

#: orgSeries-admin.php:171
msgid "About"
msgstr "Sobre"

#: orgSeries-admin.php:172
msgid "PublishPress Series Documentation"
msgstr "Documentação do PublishPress Series"

#: orgSeries-admin.php:172
msgid "Documentation"
msgstr "Documentação"

#: orgSeries-admin.php:173
msgid "Contact the PublishPress team"
msgstr "Fale com a equipe do PublishPress"

#: orgSeries-admin.php:173
msgid "Contact"
msgstr "Contato"

#: orgSeries-admin.php:344
msgid "Not part of a series"
msgstr "Não faz parte de uma série"

#: orgSeries-admin.php:406
msgid "Search series"
msgstr "Pesquisar séries"

#: orgSeries-admin.php:421
msgid "Series Part:"
msgstr "Parte da série:"

#: orgSeries-admin.php:493
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a>  (No Part Number)"
msgstr "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (sem número da parte)"

#: orgSeries-admin.php:514
msgid "View all series"
msgstr "Ver todas as séries"

#: orgSeries-admin.php:523
msgid "Manage All Series"
msgstr "Gerenciar todas as séries"

#: orgSeries-manage.php:52 orgSeries-manage.php:206 orgSeries-manage.php:238
msgid "Order"
msgstr "Ordem"

#: orgSeries-manage.php:67
msgid "Term Order"
msgstr "Ordem do termo"

#: orgSeries-manage.php:68
msgid ""
"To reposition an item, drag and drop the row by \"clicking and holding\" it "
"anywhere and moving it to its new position."
msgstr ""
"Para reposicionar um item, arraste e solte a linha “clicando e segurando” em "
"qualquer lugar e movendo-a para sua nova posição."

#: orgSeries-manage.php:184
msgid "ID"
msgstr "ID"

#: orgSeries-manage.php:210
msgid ""
"Set a specific order by entering a number (1 for first, etc.) in this field."
msgstr ""
"Defina uma ordem específica, digitando um número (1 para o primeiro, etc.) "
"neste campo."

#: orgSeries-manage.php:219 orgSeries-manage.php:276
msgid "Upload an image for the series."
msgstr "Envie uma imagem para a série."

#: orgSeries-manage.php:244
msgid ""
"Terms are usually ordered alphabetically, but you can choose your own order "
"by entering a number (1 for first, etc.) in this field."
msgstr ""
"Os termos geralmente são ordenados em ordem alfabética, mas você pode "
"escolher sua própria ordem, digitando um número (1 para o primeiro, etc.) "
"neste campo."

#: orgSeries-manage.php:251
msgid "Current series icon:"
msgstr "Ícone da série atual:"

#: orgSeries-manage.php:257
msgid "No icon currently"
msgstr "Nenhum ícone no momento"

#: orgSeries-manage.php:267
msgid ""
"Delete image? (note: there will not be an image associated with this series "
"if you select this)"
msgstr ""
"Excluir imagem? (Observação: não haverá uma imagem associada a esta série se "
"você selecionar esta opção)"

#: orgSeries-manage.php:272
msgid "Series Icon Upload:"
msgstr "Envio do ícone da série:"

#: orgSeries-manage.php:291
msgid "Invalid form data"
msgstr "Dados do formulário inválidos"

#: orgSeries-manage.php:301
msgid "Invalid nonce, reload and try again"
msgstr "Nonce inválido. Recarregue e tente novamente"

#: orgSeries-manage.php:310
msgid "Invalid taxonomy"
msgstr "Taxonomia inválida"

#: orgSeries-manage.php:316
msgid "Not enough permission"
msgstr "Permissão insuficiente"

#: orgSeries-manage.php:320
msgid "Updated successfully"
msgstr "Atualizado"

#: orgSeries-options.php:30 orgSeries-options.php:51 orgSeries-options.php:52
#: orgSeries-setup.php:729
msgid "Settings"
msgstr "Configurações"

#: orgSeries-options.php:41
msgid "PublishPress Series Options"
msgstr "Opções do PublishPress Series"

#: orgSeries-options.php:75 orgSeries-setup.php:241
msgctxt "taxonomy general name"
msgid "Series"
msgstr "Series"

#: orgSeries-options.php:76 orgSeries-setup.php:242
msgctxt "taxonomy singular name"
msgid "Series"
msgstr "Series"

#: orgSeries-options.php:77 orgSeries-setup.php:243
msgid "Search Series"
msgstr "Pesquisar séries"

#: orgSeries-options.php:78 orgSeries-setup.php:244
msgid "Popular Series"
msgstr "Séries populares"

#: orgSeries-options.php:79 orgSeries-setup.php:245 orgSeries-setup.php:291
msgid "All Series"
msgstr "Todas as séries"

#: orgSeries-options.php:80 orgSeries-setup.php:246
msgid "Edit Series"
msgstr "Editar série"

#: orgSeries-options.php:81 orgSeries-setup.php:247
msgid "Update Series"
msgstr "Atualizar série"

#: orgSeries-options.php:82 orgSeries-setup.php:248
msgid "Add New Series"
msgstr "Adicionar nova série"

#: orgSeries-options.php:83 orgSeries-setup.php:249
msgid "New Series Name"
msgstr "Nome da nova série"

#: orgSeries-options.php:85 orgSeries-setup.php:251
msgid "No series found"
msgstr "Nenhuma série encontrada"

#: orgSeries-options.php:107
msgid "Permission denied"
msgstr "Permissão negada"

#: orgSeries-options.php:114
msgid "PublishPress Series Plugin Options have been RESET"
msgstr "As opções do plugin PublishPress Series foram REDEFINIDAS"

#: orgSeries-options.php:149
msgid "PublishPress Series Plugin Options have been updated"
msgstr "As opções do plugin PublishPress Series foram atualizadas"

#: orgSeries-options.php:245
msgid "Taxonomy"
msgstr "Taxonomia"

#: orgSeries-options.php:246
msgid "Metabox"
msgstr "Metabox"

#: orgSeries-options.php:247
msgid "Advanced"
msgstr "Avançado"

#: orgSeries-options.php:259
msgid "PublishPress Series Plugin Options"
msgstr "Opções do plugin PublishPress Series"

#: orgSeries-options.php:293
msgid "Allowed Html"
msgstr "HTML permitido"

#: orgSeries-options.php:302
msgid "Overview"
msgstr "Visão geral"

#: orgSeries-options.php:304
msgid ""
"The following is a legend of the tokens that are available for use in the "
"custom template fields. These will be replaced with the appropriate values "
"when the plugin runs."
msgstr ""
"A seguir, uma legenda dos tokens que estão disponíveis para uso, nos campos "
"de modelo personalizado. Eles serão substituídos pelos valores apropriados "
"quando o plugin for executado."

#: orgSeries-options.php:306
msgid "This will be replaced with the series icon for a series."
msgstr "Isso será substituído pelo ícone da série para uma série."

#: orgSeries-options.php:308
#, no-php-format
msgid ""
"Same as %series_icon% except that the series icon will be linked to the "
"series page"
msgstr ""
"O mesmo que %series_icon%, exceto que o ícone da série será vinculado à "
"página da série"

#: orgSeries-options.php:310
msgid ""
"This token is for use with the orgSeries widget only - it references where "
"you want the list of series titles to be inserted and requires that the "
"template for each series title be also set."
msgstr ""
"Este token é para ser usado apenas com o widget “orgSeries” - ele faz "
"referência ao local onde você quer que a lista de títulos de séries seja "
"inserida e exige que o modelo para cada título de série também seja definido."

#: orgSeries-options.php:312
msgid "This will be replaced with the title of a series"
msgstr "Isso será substituído pelo título de uma série"

#: orgSeries-options.php:314
#, no-php-format
msgid ""
"Same as %series_title% except that it will also be linked to the series page"
msgstr ""
"O mesmo que %series_title%, exceto que também será vinculado à página da "
"série"

#: orgSeries-options.php:316
msgid ""
"Is the location token for where the contents of the post list post templates "
"will appear."
msgstr ""
"É o token de localização para onde o conteúdo dos modelos de posts da lista "
"de posts irá aparecer."

#: orgSeries-options.php:318
msgid ""
"Is the location token for where the contents of the post list post templates "
"will appear and use provided widget post short title."
msgstr ""
"É o token de localização para onde o conteúdo dos modelos de posts da lista "
"de posts irá aparecer e usar o título curto do post do widget fornecido."

#: orgSeries-options.php:320
msgid "Will be replaced with the post title of a post in the series"
msgstr "Será substituído pelo título de um post da série"

#: orgSeries-options.php:322
msgid ""
"Will be replaced with the post title of a post in the series linked to the "
"page view of that post."
msgstr ""
"Será substituído pelo título do post de um post da série vinculado à "
"exibição de página deste post."

#: orgSeries-options.php:324
msgid "Will be replaced with the post title short of a post in the series"
msgstr "Será substituído pelo título curto de um post da série"

#: orgSeries-options.php:326
msgid ""
"Will be replaced with the post title short of a post in the series linked to "
"the page view of that post."
msgstr ""
"Será substituído pelo título curto de um post da série vinculado à exibição "
"de página deste post."

#: orgSeries-options.php:328
msgid ""
"Will be replaced by the navigation link for the previous post in a series. "
"The text will be whatever is included in the 'Custom Previous Post "
"Navigation Text' field. If that field is empty then the text will be the "
"title of the post"
msgstr ""
"Será substituído pelo link de navegação do post anterior em uma série. O "
"texto será o que estiver incluído no campo “Texto de navegação personalizado "
"do post anterior”. Se este campo estiver vazio, o texto será o título do post"

#: orgSeries-options.php:330
msgid ""
"Will be replaced by the navigation link for the next post in a series. The "
"text will be whatever is included in the 'Custom Next Post Navigation Text' "
"field. If that field is empty then the text will be the title of the post"
msgstr ""
"Será substituído pelo link de navegação do próximo post em uma série. O "
"texto será o que estiver incluído no campo “Texto de navegação personalizado "
"do próximo post”. Se este campo estiver vazio, o texto será o título do post"

#: orgSeries-options.php:332
msgid ""
"Will be replaced by the navigation link for the first post in a series. The "
"text will be whatever is included in the 'Custom First Post Navigation Text' "
"field. If that field is empty then the text will be the title of the post"
msgstr ""
"Será substituído pelo link de navegação do primeiro post em uma série. O "
"texto será o que estiver incluído no campo “Texto de navegação personalizado "
"do primeiro post”. Se este campo estiver vazio, o texto será o título do post"

#: orgSeries-options.php:334
msgid ""
"Use this tag either before or after the rest of the template code.  It will "
"indicate where you want the content of a post to display."
msgstr ""
"Use esta tag antes ou após o restante do código do modelo. Ela irá indicar "
"onde você quer que o conteúdo de um post seja exibido."

#: orgSeries-options.php:336
msgid "Will display what part of a series the post is"
msgstr "Irá exibir a parte de uma série em que o post está"

#: orgSeries-options.php:338
msgid "Will display the total number of posts in a series"
msgstr "Irá exibir o número total de posts em uma série"

#: orgSeries-options.php:340
msgid "Will display the description for the series"
msgstr "Irá exibir a descrição da série"

#: orgSeries-options.php:379
msgid "Update Options"
msgstr "Atualizar opções"

#: orgSeries-options.php:383
msgid ""
"Clicking Yes will reset the options to the defaults and you will lose all "
"customizations. Or you can click cancel and return."
msgstr ""
"Ao clicar em \"Sim\", as opções serão redefinidas para os padrões e você "
"perderá todas as personalizações. Ou, se preferir, você pode clicar em "
"\"Cancelar\" e retornar."

#: orgSeries-options.php:384
msgid "No"
msgstr "Não"

#: orgSeries-options.php:385
msgid "Yes"
msgstr "Sim"

#: orgSeries-options.php:398
msgid ""
"These settings allow you to customize the main frontend screens in "
"PublishPress Series."
msgstr ""
"Estas configurações permitem que você personalize as principais telas da "
"interface do PublishPress Series."

#: orgSeries-options.php:405
msgid ""
"These templates allow you to customize the frontend appearance of "
"PublishPress Series."
msgstr ""
"Estes modelos permitem que você personalize a aparência da interface do "
"PublishPress Series."

#: orgSeries-options.php:412
msgid ""
"This section is for the icons that show with your series. Note that you must "
"use a token for the icon in the \"Templates\" settings."
msgstr ""
"Esta seção é para os ícones que são mostrados com a sua série. Observe que "
"você precisa usar um token para o ícone nas configurações de \"Modelos\"."

#: orgSeries-options.php:419
msgid ""
"Please change these settings carefully as they make significant changes to "
"PublishPress Series."
msgstr ""
"Altere estas configurações com cuidado, pois elas fazem alterações "
"significativas no PublishPress Series."

#: orgSeries-options.php:426
msgid ""
"This feature allows you to create a new taxonomy for this plugin to use if "
"you don't want to use the default \"Series\" taxonomy."
msgstr ""
"Este recurso permite que você crie uma nova taxonomia para este plugin usar, "
"se não quiser usar a taxonomia padrão \"Series\"."

#: orgSeries-options.php:433
msgid ""
"These settings allow you to customize the metabox on the post editing screen."
msgstr ""
"Estas configurações permitem que você personalize a metabox na tela de "
"edição de posts."

#: orgSeries-options.php:445
msgid "Default"
msgstr "Padrão"

#: orgSeries-options.php:446
msgid "Grid"
msgstr "Grade"

#: orgSeries-options.php:447
msgid "List"
msgstr "Lista"

#: orgSeries-options.php:454
msgid "Display on single posts in a series"
msgstr "Exibição em \"posts únicos\" de uma série"

#: orgSeries-options.php:455
msgid "Choose the design for pages that are included in a series."
msgstr "Escolha o design para as páginas que estão incluídas em uma série."

#: orgSeries-options.php:460
msgid "Display Series Post List?"
msgstr "Exibir lista de posts da série?"

#: orgSeries-options.php:464
msgid "Maximum number of items in Series Post List"
msgstr "Número máximo de itens na lista de posts da série"

#: orgSeries-options.php:468
msgid "Display Series Navigation?"
msgstr "Exibir navegação da série?"

#: orgSeries-options.php:472
msgid "Display Series Meta?"
msgstr "Exibir meta da série?"

#: orgSeries-options.php:476
msgid "Use PublishPress Series CSS styles?"
msgstr "Usar estilos CSS do PublishPress Series?"

#: orgSeries-options.php:480
msgid "Style options"
msgstr "Opções de estilo"

#: orgSeries-options.php:482
msgid "Use default style"
msgstr "Usar estilo padrão"

#: orgSeries-options.php:483
msgid "Use box style"
msgstr "Usar estilo em caixa"

#: orgSeries-options.php:484
msgid "Use dark style"
msgstr "Usar estilo escuro"

#: orgSeries-options.php:485
msgid "Use light style"
msgstr "Usar estilo claro"

#: orgSeries-options.php:491
msgid "Display on Series Overview screens"
msgstr "Exibição nas telas de \"Visão geral\" da série"

#: orgSeries-options.php:492
msgid ""
"Choose the design for the taxonomy page where a single Series is displayed."
msgstr ""
"Escolha o design da página de taxonomia em que uma única série é exibida."

#: orgSeries-options.php:498
msgid "Layout:"
msgstr "Layout:"

#: orgSeries-options.php:516
#, php-format
msgid ""
"Choosing a layout different to \"Default\" will override the taxonomy "
"template from your theme. <a href=\"%s\" target=\"_blank\">Click here for "
"details on how to customize these designs</a>."
msgstr ""
"A escolha de um layout diferente de \"Padrão\" irá substituir o modelo de "
"taxonomia do seu tema. <a href=\"%s\" target=\"_blank\">Clique aqui para "
"mais detalhes sobre como personalizar estes designs</a>."

#: orgSeries-options.php:526
msgid "Columns:"
msgstr "Colunas:"

#: orgSeries-options.php:533
msgid "Order series by:"
msgstr "Ordenar a série por:"

#: orgSeries-options.php:535
msgid "Series part"
msgstr "Parte da série"

#: orgSeries-options.php:536
msgid "Order by date"
msgstr "Ordenar por data"

#: orgSeries-options.php:540
msgid "Series order method"
msgstr "Método de ordenação das séries"

#: orgSeries-options.php:542 orgSeries-widgets.php:320
msgid "Ascending"
msgstr "Ascendente"

#: orgSeries-options.php:543 orgSeries-widgets.php:319
msgid "Descending"
msgstr "Descendente"

#: orgSeries-options.php:547
msgid "Display on Series Table of Contents screens"
msgstr "Exibição nas telas da \"Tabela de conteúdos\" da série"

#: orgSeries-options.php:548
msgid "Choose the design for the page where all your Series are displayed."
msgstr "Escolha o design da página em que todas as suas séries serão exibidas."

#: orgSeries-options.php:550
msgid "Series Table of Contents URL:"
msgstr "URL da tabela de conteúdos da série:"

#: orgSeries-options.php:563
#, php-format
msgid ""
"You must %1s update your permalink structure %2s to something other than "
"\"Plain\" for the Series Table of Contents URL to work."
msgstr ""
"Você precisa %1s atualizar sua estrutura de links permanentes %2s para algo "
"diferente de \"Simples\" para que o URL da tabela de conteúdos da série "
"funcione."

#: orgSeries-options.php:584
msgid "Series Per Page:"
msgstr "Séries por página:"

#: orgSeries-options.php:588
msgid "Series Table of Contents Title:"
msgstr "Título da tabela de conteúdos da série:"

#: orgSeries-options.php:607
msgid "As in Template"
msgstr "Como no modelo"

#: orgSeries-options.php:608
msgid "Top"
msgstr "Superior"

#: orgSeries-options.php:609
msgid "Bottom"
msgstr "Inferior"

#: orgSeries-options.php:620
msgid "Series Post List Box"
msgstr "Caixa da lista de posts da série"

#: orgSeries-options.php:622 orgSeries-options.php:661
msgid "This display is shown at the top of all posts in a series."
msgstr ""
"Esta exibição é mostrada na parte superior de todos os posts de uma série."

#: orgSeries-options.php:625
msgid "Series Post List"
msgstr "Lista de posts da série"

#: orgSeries-options.php:630
msgid "Series Post List box Location"
msgstr "Localização da caixa da lista de posts da série"

#: orgSeries-options.php:645
msgid "Series Post List Post Title (Linked Post)"
msgstr "Título do post da lista de posts da série (post vinculado)"

#: orgSeries-options.php:651
msgid "Series Post List Post Title (Current Post)"
msgstr "Título do post da lista de posts da série (post atual)"

#: orgSeries-options.php:659
msgid "Series Meta Box"
msgstr "Metabox da série"

#: orgSeries-options.php:665
msgid "Series Meta:"
msgstr "Meta da série:"

#: orgSeries-options.php:671
msgid "Series Metabox Location"
msgstr "Localização da metabox da série"

#: orgSeries-options.php:686
msgid "Series Meta (with excerpts):"
msgstr "Meta da série (com resumos):"

#: orgSeries-options.php:690
msgid ""
"This control how and what series meta information is displayed with posts "
"that are part of a series when the_excerpt is used. "
msgstr ""
"Isso controla como e quais meta informações da série são exibidas com posts "
"que fazem parte de uma série quando \"the_excerpt\" é usado. "

#: orgSeries-options.php:695
msgid "Limit series meta to single page only"
msgstr "Limitar os metadados da série a uma única página"

#: orgSeries-options.php:697
msgid ""
"Whether to limit series meta display to single page only or include archive "
"page."
msgstr ""
"Se a exibição dos metadados da série deve ser limitada a uma única página ou "
"incluir a página de arquivo."

#: orgSeries-options.php:703
msgid "Series Navigation Box"
msgstr "Caixa de navegação da série"

#: orgSeries-options.php:705
msgid "This display is shown at the bottom of all posts in a series."
msgstr ""
"Esta exibição é mostrada na parte inferior de todos os posts de uma série."

#: orgSeries-options.php:709
msgid "Series Post Navigation:"
msgstr "Navegação do post da série:"

#: orgSeries-options.php:714
msgid "Series Post Navigation Location"
msgstr "Localização da navegação do post da série"

#: orgSeries-options.php:729
msgid "Next Post"
msgstr "Próximo post"

#: orgSeries-options.php:734
msgid "Previous Post"
msgstr "Post anterior"

#: orgSeries-options.php:739
msgid "First Post"
msgstr "Primeiro post"

#: orgSeries-options.php:747 orgSeries-widgets.php:13
msgid "Latest Series"
msgstr "Série mais recente"

#: orgSeries-options.php:749
msgid "This display is used by the \"Latest Series\" widget."
msgstr "Esta exibição é usada pelo widget \"Série mais recente\"."

#: orgSeries-options.php:753
msgid "Latest Series (tags before):"
msgstr "Série mais recente (tags anteriores):"

#: orgSeries-options.php:758
msgid "Latest Series (inner tags):"
msgstr "Série mais recente (tags internas):"

#: orgSeries-options.php:763
msgid "Latest Series (tags after):"
msgstr "Série mais recente (tags posteriores):"

#: orgSeries-options.php:771 orgSeries-options.php:777 orgSeries-setup.php:421
#: orgSeries-widgets.php:112
msgid "Series Table of Contents"
msgstr "Tabela de conteúdos da série"

#: orgSeries-options.php:773
msgid ""
"This display is used by the \"Series Table of Contents\" widget, shortcode, "
"and URL."
msgstr ""
"Esta exibição é usada pelo widget \"Tabela de conteúdos da série\", "
"shortcode e URL."

#: orgSeries-options.php:780
msgid ""
"This display is used by the \"Series Table of Contents\". To find the URL "
"for this display, go the \"Display\" tab and then \"Series Table of Contents "
"URL\"."
msgstr ""
"Esta exibição é usada pela \"Tabela de conteúdo da série\". Para encontrar o "
"URL desta exibição, acesse a aba \"Exibir\" e depois \"URL da tabela de "
"conteúdos da série\"."

#: orgSeries-options.php:807
msgid "Width for icon on series table of contents page (in pixels)"
msgstr "Largura do ícone na página da tabela de conteúdos da série (em pixels)"

#: orgSeries-options.php:813
msgid "Width for icon on a post page (in pixels)."
msgstr "Largura do ícone em uma página de post (em pixels)."

#: orgSeries-options.php:819
msgid "Width for icon if displayed via the latest series template (in pixels)."
msgstr ""
"Largura do ícone, se exibido através do modelo da série mais recente (em "
"pixels)."

#: orgSeries-options.php:843
msgid "Series Taxonomy Slug:"
msgstr "Slug da taxonomia da série:"

#: orgSeries-options.php:846
msgid "This text will be part of the series base URL."
msgstr "Este texto fará parte do URL base da série."

#: orgSeries-options.php:851
msgid "Series Taxonomy:"
msgstr "Taxonomia da série:"

#: orgSeries-options.php:855
msgid ""
"To create a new taxonomy, enter the new name and click the \"Update "
"Options\" button."
msgstr ""
"Para criar uma nova taxonomia, digite o novo nome e clique no botão "
"\"Atualizar opções\"."

#: orgSeries-options.php:862
msgid "Migrate"
msgstr "Migrar"

#: orgSeries-options.php:866
msgid "Migrate series to new taxonomy"
msgstr "Migrar séries para uma nova taxonomia"

#: orgSeries-options.php:868
msgid ""
"Please use with caution. Running this process will delete all the terms from "
"the current taxonomy and migrate them to a new taxonomy."
msgstr ""
"Use com cuidado. A execução deste processo irá excluir todos os termos da "
"taxonomia atual e migrá-los para uma nova taxonomia."

#: orgSeries-options.php:885
msgid "Default Series Order"
msgstr "Ordem da série padrão"

#: orgSeries-options.php:886
msgid "Alphabetical A-Z"
msgstr "Ordem alfabética de A a Z"

#: orgSeries-options.php:887
msgid "Alphabetical Z-A"
msgstr "Ordem alfabética de Z a A"

#: orgSeries-options.php:893
msgid "Show \"Add New\""
msgstr "Mostrar \"Adicionar novo\""

#: orgSeries-options.php:897
msgid "Show \"Post title in widget\""
msgstr "Mostrar “Título do post no widget”"

#: orgSeries-options.php:901
msgid "Metabox Series Order"
msgstr "Ordem da série na metabox"

#: orgSeries-options.php:938
msgid "Series Settings"
msgstr "Configurações do Series"

#: orgSeries-options.php:944
msgid ""
"Delete all PublishPress Series data from the database when deleting this "
"plugin."
msgstr ""
"Exclua todos os dados do PublishPress Series do banco de dados ao excluir "
"este plugin."

#: orgSeries-options.php:951
msgid "Series Upgrade"
msgstr "Atualização do Series"

#: orgSeries-options.php:956
msgid "Run Upgrade Task"
msgstr "Executar tarefa de atualização"

#: orgSeries-options.php:960
msgid ""
"In version 2.11.4, PublishPress Series made changes to how series are "
"stored. You can run the upgrade task here if you're having issues with "
"series parts."
msgstr ""
"Na versão 2.11.4, do PublishPress Series, foram feitas alterações na forma "
"como as séries são armazenadas. Você pode executar a tarefa de atualização "
"aqui, se estiver tendo problemas com as partes da série."

#: orgSeries-options.php:969
msgid "Reset settings"
msgstr "Redefinir configurações"

#: orgSeries-options.php:972
msgid "Reset options to default"
msgstr "Redefinir opções para o padrão"

#: orgSeries-options.php:991 orgSeries-options.php:1013
msgid "Please update your PublishPress Series data"
msgstr "Atualize seus dados do PublishPress Series"

#: orgSeries-options.php:993 orgSeries-options.php:1014
msgid ""
"We have made changes to how series are stored and this requires a small "
"update. This improves support for the Multiple Series feature, and resolves "
"some issues with the order of posts. Please click this button to upgrade."
msgstr ""
"Fizemos alterações na forma como as séries são armazenadas e isso exige uma "
"pequena atualização. Isso melhora o suporte ao recurso “Várias séries” e "
"resolve alguns problemas com a ordem dos posts. Clique neste botão para "
"atualizar."

#: orgSeries-options.php:994 orgSeries-options.php:1015
msgid "Update Series data"
msgstr "Atualizar dados da série"

#: orgSeries-options.php:1061
msgid "Series upgrade completed."
msgstr "Atualização da série concluída."

#: orgSeries-setup.php:192
msgid "Select \"Not part of a series\" to remove any series data from post"
msgstr ""
"Selecione \"Não faz parte de uma série\" para remover quaisquer dados de "
"série do post"

#: orgSeries-setup.php:252
msgid "&larr; Go to Series"
msgstr "&larr; Ir para a série"

#: orgSeries-setup.php:253
msgid "View Series"
msgstr "Ver séries"

#: orgSeries-setup.php:255
msgid "Series List"
msgstr "Lista de séries"

#: orgSeries-setup.php:256
msgid "Series Link"
msgstr "Link da série"

#: orgSeries-setup.php:257
msgid "A link to a Series"
msgstr "Um link para uma série"

#: orgSeries-setup.php:297 orgSeries-setup.php:298
msgctxt "leave the %tokens% as is when translating"
msgid ""
"This entry is part %series_part% of %total_posts_in_series% in the series "
msgstr ""
"Esta entrada é parte da %series_part% do %total_posts_in_series% na série "

#: orgSeries-setup.php:303
msgid "Series Navigation"
msgstr "Navegação da série"

#: orgSeries-setup.php:715
msgid "Series: "
msgstr "Séries: "

#: orgSeries-setup.php:717
msgid "Posts from the series: "
msgstr "Posts da série: "

#: orgSeries-taxonomy.php:659
msgid "Part:"
msgstr "Parte:"

#: orgSeries-template-tags.php:216
msgid "All the Series I've Written"
msgstr "Todas as séries que escrevi"

#: orgSeries-template-tags.php:220 orgSeries-template-tags.php:229
#, php-format
msgid "<a href=\"%s\" title=\"%s\">Series</a>"
msgstr "<a href=\"%s\" title=\"%s\">Série</a>"

#: orgSeries-utility.php:410
msgid "Auto/None"
msgstr "Automático/nenhum"

#: orgSeries-widgets.php:12
msgid "Use this to control the output of the latest series widget"
msgstr "Use isso para controlar a exibição do widget da série mais recente"

#: orgSeries-widgets.php:20
msgid "Most Recent Series"
msgstr "Série mais recente"

#: orgSeries-widgets.php:73 orgSeries-widgets.php:269
msgid "Title:"
msgstr "Titulo:"

#: orgSeries-widgets.php:79
#, php-format
msgid ""
"The layout and content of this widget can be adjusted via the <a "
"href=\"%s\">Latest Series</a> area."
msgstr ""
"O layout e o conteúdo deste widget, podem ser ajustados através da área <a "
"href=\"%s\">Série mais recente</a>."

#: orgSeries-widgets.php:82
msgid "Hide series with no posts?"
msgstr "Ocultar séries sem posts?"

#: orgSeries-widgets.php:85 orgSeries-widgets.php:295
msgid "Order by:"
msgstr "Ordenar por:"

#: orgSeries-widgets.php:87
msgid "Number of posts in Series"
msgstr "Número de posts na série"

#: orgSeries-widgets.php:88
msgid "Name of Series"
msgstr "Nome da série"

#: orgSeries-widgets.php:89
msgid "Series Slug"
msgstr "Slug da série"

#: orgSeries-widgets.php:90
msgid "When Series was Created"
msgstr "Quando a série foi criada"

#: orgSeries-widgets.php:91
msgid "Random"
msgstr "Aleatório"

#: orgSeries-widgets.php:95
msgid "Number of series to display:"
msgstr "Número de séries a serem exibidas:"

#: orgSeries-widgets.php:99
msgid "Display Order: "
msgstr "Ordem de exibição: "

#: orgSeries-widgets.php:100
msgid "ASC: "
msgstr "ASC: "

#: orgSeries-widgets.php:101
msgid "DESC: "
msgstr "DESC: "

#: orgSeries-widgets.php:111
msgid "Use this to display the Series Table of contents"
msgstr "Use esta opção para exibir a tabela de conteúdos da série"

#: orgSeries-widgets.php:178
msgid "Select Series"
msgstr "Selecionar série"

#: orgSeries-widgets.php:234
msgid "Other posts in series:"
msgstr "Outros posts na série:"

#: orgSeries-widgets.php:274
msgid "Show Series Table Of Content"
msgstr "Mostrar a tabela de conteúdos da série"

#: orgSeries-widgets.php:279
msgid "Show Series Table Of Content as:"
msgstr "Mostrar a tabela de conteúdos da série como:"

#: orgSeries-widgets.php:280
msgid "Dropdown:"
msgstr "Menu suspenso:"

#: orgSeries-widgets.php:281
msgid "List:"
msgstr "Lista:"

#: orgSeries-widgets.php:285
msgid "Show post count:"
msgstr "Mostrar contagem de posts:"

#: orgSeries-widgets.php:290
msgid "Hide empty series:"
msgstr "Ocultar séries vazias:"

#: orgSeries-widgets.php:299
msgid "name"
msgstr "nome"

#: orgSeries-widgets.php:300
msgid "count"
msgstr "contagem"

#: orgSeries-widgets.php:301
msgid "slug"
msgstr "slug"

#: orgSeries-widgets.php:302
msgid "series id"
msgstr "id da série"

#: orgSeries-widgets.php:315
msgid "Order:"
msgstr "Ordem:"

#: orgSeries-widgets.php:333
msgid "Exclude series:"
msgstr "Excluir séries:"

#: orgSeries-widgets.php:348
msgid "Include series:"
msgstr "Incluir séries:"

#: orgSeries-widgets.php:363
msgid "Number of Series:"
msgstr "Número de séries:"

#: orgSeries-widgets.php:368
msgid "Offset:"
msgstr "Deslocamento:"

#: orgSeries-widgets.php:370
msgid ""
"You can select the offset for the number of series (useful for paging).  No "
"offset if left blank"
msgstr ""
"Você pode selecionar o deslocamento para o número de séries (útil para "
"paginação). Nenhum deslocamento, se for deixado em branco"

#: orgSeries-widgets.php:374
msgid "Search:"
msgstr "Pesquisar:"

#: orgSeries-widgets.php:376
msgid ""
"You can return any series that match this search string (matched against "
"series names)"
msgstr ""
"Você pode retornar qualquer série que corresponda a esta string de pesquisa "
"(comparada com os nomes das séries)"

#: orgSeries-widgets.php:380
msgid "Show other posts in the current series:"
msgstr "Mostrar outros posts na série atual:"

#: orgSeries-widgets.php:397
msgid "Current series"
msgstr "Série atual"

#: orgSeries-widgets.php:416
msgid "Series widget title:"
msgstr "Título do widget da série:"

#: orgSeries-widgets.php:418
msgid "This text will display above other posts in this series."
msgstr "Este texto será exibido acima de outros posts desta série."

#: src/application/Container.php:161
#, php-format
msgid "The %1$s already has a parameter indexed with the name: %2$s."
msgstr "O %1$s já tem um parâmetro indexado com o nome: %2$s."

#: src/application/Root.php:140
#, php-format
msgid "The %1$s method can only be used to register a child of %2$s."
msgstr ""
"O método %1$s apenas pode ser usado para cadastrar um descendente de %2$s."

#: src/domain/exceptions/EntityNotFoundException.php:31
#, php-format
msgid "Unable to retrieve an instance of %1$s. Not found."
msgstr "Não foi possível recuperar uma instância de %1$s. Não encontrada."

#: src/domain/exceptions/InvalidEntityException.php:30
#, php-format
msgid "%1$s is not a valid entity (expected: %2$s)."
msgstr "%1$s não é uma entidade válida (esperado: %2$s)."

#: src/domain/exceptions/InvalidFilePathException.php:28
#, php-format
msgid ""
"The \"%1$s\" file is either missing or could not be read due to permissions. "
"Please ensure that the following path is correct and verify that the file "
"permissions are correct:%2$s %3$s"
msgstr ""
"O arquivo \"%1$s\" está ausente ou não pôde ser lido devido às permissões. "
"Certifique-se de que o caminho a seguir esteja correto e verifique se as "
"permissões do arquivo estão corretas:%2$s %3$s"

#: src/domain/exceptions/InvalidInterfaceException.php:27
#, php-format
msgid "%s does not exist or is not reachable."
msgstr "%s não existe ou não está acessível."

#: src/domain/exceptions/NonceFailException.php:19
msgid "Nonce fail."
msgstr "Falha de nonce."

#: src/domain/model/ControllerRoute.php:51
#: src/domain/model/HasHooksRoute.php:60
#, php-format
msgid ""
"The provided object fully qualified class name (%1$s) must implement the "
"%2$s interface."
msgstr ""
"O nome da classe totalmente qualificada do objeto fornecido (%1$s) precisa "
"implementar a interface %2$s."

#: src/domain/model/FileLocation.php:50
#, php-format
msgid "The given file path (%s) is not readable."
msgstr "O caminho do arquivo fornecido (%s) não é legível."

#: src/domain/model/LicenseKeyRepository.php:131
msgid "An error occurred, please try again."
msgstr "Ocorreu um erro. Tente novamente."

#: src/domain/model/RouteIdentifier.php:52
#, php-format
msgid "The incoming value for %1$s is expected to be a closure.  It was not."
msgstr ""
"É esperado que o valor de entrada para %1$s seja um fechamento. Mas não foi."

#: src/domain/services/AssetRegistry.php:229
#, php-format
msgid ""
"The value for %1$s is already set and it is not an array. The %2$s method "
"can only be used to push values to this data element when it is an array."
msgstr ""
"O valor de %1$s já está definido e não é uma array. O método %2$s apenas "
"pode ser usado para enviar valores para este elemento de dados quando ele "
"for uma array."

#: src/domain/services/AssetRegistry.php:272
#, php-format
msgid ""
"The value for %1$s already exists in the %2$s property. Overrides are not "
"allowed. Since the value of this data is an array, you may want to use the "
"%3$s method to push your value to the array."
msgstr ""
"O valor de %1$s já existe na propriedade %2$s. Substituições não são "
"permitidas. Como o valor destes dados é uma array, talvez você queira usar o "
"método %3$s para enviar seu valor para a array."

#: src/domain/services/AssetRegistry.php:284
#, php-format
msgid ""
"The value for %1$s already exists in the %2$s property. Overrides are not "
"allowed.  Consider attaching your value to a different key"
msgstr ""
"O valor de %1$s já existe na propriedade %2$s. Substituições não são "
"permitidas. Considere anexar seu valor a uma chave diferente"

#: src/domain/services/AssetRegistry.php:409
#, php-format
msgid ""
"The namespace for this manifest file has already been registered, choose a "
"namespace other than %s"
msgstr ""
"O namespace para este arquivo de manifesto já foi cadastrado, escolha um "
"namespace diferente de %s"

#: src/domain/services/AssetRegistry.php:420
#, php-format
msgid ""
"The provided value for %1$s is not a valid url.  The url provided was: %2$s"
msgstr ""
"O valor fornecido para %1$s não é um URL válido. O URL fornecido foi: %2$s"

#: src/domain/services/admin/LicenseKeyFormManager.php:241
msgid "License Key successfully activated."
msgstr "Chave de licença ativada."

#: src/domain/services/admin/LicenseKeyFormManager.php:242
msgid "License Key successfully deactivated."
msgstr "Chave de licença desativada."

#: src/libraries/edd/PluginUpdater.php:203
#, php-format
msgid ""
"There is a new version of %1$s available. %2$sView version %3$s details%4$s."
msgstr ""
"Há uma nova versão do %1$s disponível. %2$sVer detalhes da versão %3$s%4$s."

#: src/libraries/edd/PluginUpdater.php:211
#, php-format
msgid ""
"There is a new version of %1$s available. %2$sView version %3$s details%4$s "
"or %5$supdate now%6$s."
msgstr ""
"Há uma nova versão do %1$s disponível. %2$sVer detalhes da versão %3$s%4$s "
"ou %5$satualizar agora%6$s."

#: src/libraries/edd/PluginUpdater.php:398
msgid "You do not have permission to install plugin updates"
msgstr "Você não tem permissão para instalar atualizações de plugins"

#: src/libraries/edd/PluginUpdater.php:398
msgid "Error"
msgstr "Erro"

#: src/views/admin/templates/license_key_form.template.php:16
#, php-format
msgid "License Key for %1$s"
msgstr "Chave de licença para %1$s"

#: src/views/admin/templates/license_key_form.template.php:19
msgid "Enter your license key: "
msgstr "Digite sua chave de licença: "

#. Plugin Name of the plugin/theme
msgid "PublishPress Series Pro"
msgstr "PublishPress Series Pro"

#. Plugin URI of the plugin/theme
msgid "https://publishpress.com/publishpress-series/"
msgstr "https://publishpress.com/publishpress-series/"

#. Description of the plugin/theme
msgid ""
"PublishPress Series allows you to group content together into a series. This "
"is ideal for magazines, newspapers, short-story writers, teachers, comic "
"artists, or anyone who writes multiple posts on the same topic."
msgstr ""
"O PublishPress Series permite que você agrupe o conteúdo em uma série. Isso "
"é ideal para revistas, jornais, escritores de contos, professores, artistas "
"de quadrinhos ou qualquer pessoa que escreva vários posts sobre o mesmo "
"tópico."

#. Author of the plugin/theme
msgid "PublishPress"
msgstr "PublishPress"

#. Author URI of the plugin/theme
msgid "https://publishpress.com/"
msgstr "https://publishpress.com/"
