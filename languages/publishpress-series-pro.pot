#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PublishPress Series Pro\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-14 08:52+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: \n"
"Language: \n"
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.5.5; wp-6.0\n"
"X-Domain: publishpress-series-pro"

#: includes-pro/addons/multiples/os-multi-setup.php:343
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (No Part Number)"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:336
#, php-format
msgid "<a href=\"%1$s\" title=\"%2$s\">%3$s</a> (No part number)"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:339
#, php-format
msgid "<a href=\"%3$s\" title=\"%4$s\">%5$s</a> (Part %1$s of %2$s)"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:305
msgid ""
"A short title of this post that will be used in the Series widget. Leave "
"blank to use the full title."
msgstr ""

#: includes-pro/classes/licence.php:112 includes-pro/classes/licence.php:130
#: includes-pro/classes/licence.php:140
msgid "Activate License"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:67
msgid "Add New"
msgstr ""

#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:98
msgid "Custom Post Type Support"
msgstr ""

#: includes-pro/classes/licence.php:124
msgid "Deactivate License"
msgstr ""

#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:109
msgid "Enable PublishPress Series for custom post types."
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:240
msgid ""
"Hmm... it looks like there is already a series with that name. Try something "
"else"
msgstr ""

#. Author URI of the plugin
msgid "https://publishpress.com/"
msgstr ""

#. URI of the plugin
msgid "https://publishpress.com/publishpress-series/"
msgstr ""

#: includes-pro/classes/licence.php:93
msgid "License key:"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:370
msgid "No Series"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:293
msgid "Part"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:303
msgid "Post title in widget:"
msgstr ""

#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:96
#: includes-pro/addons/cpt-support/class/OS_CPT_Support.class.php:119
msgid "Post Types"
msgstr ""

#. Author of the plugin
msgid "PublishPress"
msgstr ""

#. Description of the plugin
msgid ""
"PublishPress Series allows you to group content together into a series. This "
"is ideal for magazines, newspapers, short-story writers, teachers, comic "
"artists, or anyone who writes multiple posts on the same topic."
msgstr ""

#. Name of the plugin
msgid "PublishPress Series Pro"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:264
#: includes-pro/addons/multiples/os-multi-setup.php:292
msgid "Series"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:91
msgid "Series:"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:224
msgid "Sorry but you don't have permission to add series"
msgstr ""

#: includes-pro/classes/licence.php:100
msgid "Status"
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:235
msgid ""
"The name you picked isn't sanitizing correctly. Try something different."
msgstr ""

#: includes-pro/addons/multiples/os-multi-setup.php:69
msgid "To remove a post from series, just deselect any checkboxes"
msgstr ""

#: includes-pro/addons/extra-tokens/legacy-includes.php:87
msgid "Will display the post author of the post in the series"
msgstr ""

#: includes-pro/addons/extra-tokens/legacy-includes.php:90
msgid "Will display the post thumbnail of a post belonging to the series"
msgstr ""

#: includes-pro/addons/extra-tokens/legacy-includes.php:93
msgid "Will display the published date of a post within a series"
msgstr ""

#: includes-pro/addons/extra-tokens/legacy-includes.php:84
msgid "Will display the series id of the series"
msgstr ""

#: includes-pro/addons/extra-tokens/legacy-includes.php:81
msgid "Will display the slug for the series"
msgstr ""

#: includes-pro/classes/licence.php:103
msgid "Your license key provides access to updates and support."
msgstr ""
