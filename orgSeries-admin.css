.publishpress-series-permalink-error {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-left-width: 4px;
    box-shadow: 0 1px 1px rgb(0 0 0 / 4%);
    border-left-color: #d63638;
    margin: 5px 0 15px;
    padding: 5px 5px;
}

#newseriesdiv .inside,
#seriesdiv .inside {
    margin: 0 !important;
    padding: 0 !important;
    border: 0 !important;
}

#series-multiples-add {
    display: flex;
}

.taxonomy-series .fixed .column-series_order {
    width: 100px;
}
.taxonomy-series .fixed .column-description, 
.taxonomy-series .fixed .column-group {
    width: 120px;
}
.taxonomy-series .fixed .column-slug {
    width: initial;
}
.taxonomy-series .fixed .column-posts {
    text-align: left;
}

#serieschecklist li label input:checked ~ .selected-series-order {
    display: inline-block !important;
    margin-left: 5px;
}

.ppseries-settings-tab-content .stuffbox > h3, 
.ppseries-settings-tab-content .postbox > h3, 
.ppseries-settings-tab-content h3.handle, 
.ppseries-settings-tab-content h2.handle {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
}

.ppseries-settings-tab-content .description {
    font-weight: initial;
}

.ppseries-textarea {
    resize: none;
    height: 70px;
}
.pp-widget-series {
    border-bottom: solid 1px #dcdcde;
    list-style: none;
    margin: 0 !important;
    overflow: auto;
    padding: 10px !important;
    border: solid 1px #dcdcde;
    background-color: #fff;
    padding-bottom: 10px;
    padding-top: 10px;
    max-height: 12em;
}
.pp-widget-series-li {
    margin: 0;
    padding: 0;
    line-height: 1.69230769;
    word-wrap: break-word;
}
.ppseries-settings-table input[type=text], 
.ppseries-settings-table input[type=number], 
.ppseries-settings-table select {
    width: 300px;
}
.ppseries-full-width {
    width: -webkit-fill-available !important;
    width: -moz-available !important;
}
.post-series {
    display: inline;
    margin: 0;
    padding: 0;
}

.post-series li {
    display: inline;
}
ul#serieschecklist {
    max-height: 12em;
    overflow: auto;
    padding-bottom: 10px;
}
ul#serieschecklist li span.li-series-name {
    padding-left: 0;
}
ul#serieschecklist li label {
    clear: both;
    font-size: 13px;
}
ul#serieschecklist li {
    margin-bottom: 6px !important;
}

.series-metadiv div.tabs-panel {
    overflow: auto;
    padding: 0 .9em;
    border: solid 1px #dcdcde;
    background-color: #fff;
    padding-bottom: 10px;
    padding-top: 10px;
}

#ajaxseries input[type="text"] {
    width: 62%;
    box-shadow: 0 0 0 transparent;
    border-radius: 4px;
    border: 1px solid #8c8f94;
    background-color: #fff;
    color: #2c3338;
    padding: 0 8px;
    line-height: 2;
    min-height: 30px;
    margin: 0 4px 0 0;
    line-height: 2.15384615;
    min-height: 30px;
    margin-bottom: 1px;
}
#serie_post_shorttitle {
  width: 100%;
}

#serieshowto {
    font-size: 11px;
    margin: 0 5px;
    display: block;
}

#part-description {
    font-size: 11px;
    margin: 0 5px;
    display: block;
    margin-left: 0;
}

#seriespart input, #seriespart input:focus {
    border: 1px solid #ccc;
}

#newseries {
    margin-right: 5px;
}

.series-added-indicator {
    background-color: yellow;
}

input #seriesadd {
    background: #a4a4a4;
    border-bottom: 1px solid #898989;
    border-left: 1px solid #bcbcbc;
    border-right: 1px solid #898989;
    border-top: 1px solid #bcbcbc;
    color: #fff;
    font-size: 10px;
    padding: 0;
    margin: 0;
    font-weight: bold;
    height: 20px;
    margin-bottom: 2px;
    text-align: center;
    width: 37px;
}

#seriessearchform, #post-query-submit2 {
    float: left;
    margin: 0 0 0 15px;
    position: relative;
    top: 0;
}

#post-query-submit2 {
    margin: 18px 0 0;
}

#seriessearchform fieldset {
    float: left;
    margin: 0 1.5ex 1em 0;
    padding: 0;
}

#seriessearchform fieldset legend {
    padding: 0 0 .2em 1px;
}

.org-series-side-info {
    float: left;
    margin-left: 10px;
    margin-right: 15px;
    width: 20%;
    background: #E4E4E4;
    padding: 5px;
}

.orgseriesleft {
    float: left;
    width: 60%;
}

#topic-toc-settings-series-template-core .template {
    width: 100%;
}

.seriesdescr {
    width: 20%;
}

#seriesdiv .inside {
    margin: 0 !important;
    padding: 0 !important;
}

/** License Key Containers **/
.os-license-key-container {
    background-color: #ebebeb;
    margin: 0;
    padding:1px 10px 10px;
    margin-bottom: 10px;
}

.os-license-key-container input[type="text"] {
    width: 100%;
}

.license-key-submit-button {
    margin-top: 10px;
}

.ppseries-warning {
    border: 1px solid rgb(230, 219, 84);
    color: #8a8a8a;
    font-size: 13px;
    padding: 12px 25px;
    background-color: rgb(254, 255, 224);
}

.ppseries-hide-content {
  display: none !important;
}

.ppseries-settings-header {
  padding-left: 0 !important;
}

.ppseries-spinner {
    float: none !important;
}
.ppseries-settings-tab-content .form-table th {
    width: 300px !important;
}
.ppseries-widget-paragraph {
    font-size: 13px !important;
}

.ppseries-advertisement-right-sidebar .upgrade-btn a {
    background: #FCB223;
    color: #000 !important;
    font-weight: normal;
    text-decoration: none;
    padding: 9px 12px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    box-sizing: border-box;
    border: 1px solid #fca871;
    break-inside: avoid;
    white-space: nowrap;
}

.ppseries-advertisement-right-sidebar .upgrade-btn a:hover {
    background: #fcca46;
    color: #000 !important;
}

.ppseries-advertisement-right-sidebar h3.hndle {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
}

.ppseries-token-right-sidebar h3.hndle {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
}

.ppseries-advertisement-right-sidebar h3.hndle {
    font-size: 14px;
    padding: 8px 12px;
    margin: 0;
    line-height: 1.4;
}

.ppseries-advertisement-right-sidebar .postbox-container .inside ul {
    margin-bottom: 20px;
}

.ppseries-advertisement-right-sidebar .postbox-container .inside ul li {
    position: relative;
    padding-left: 22px;
    font-weight: 600;
    font-size: .9em;
}

.ppseries-advertisement-right-sidebar .postbox-container .inside ul li:before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 0;
    background-color: #3C50FF;
    mask-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z'/></svg>");
    -webkit-mask-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z'/></svg>");
    mask-size: 16px;
    -webkit-mask-size: 16px;
    mask-repeat: no-repeat;
    -webkit-mask-repeat: no-repeat;
    mask-position: left;
    -webkit-mask-position: left;
}

.ppseries-advertisement-right-sidebar a.advert-link,
.ppseries-content-promo-box a.advert-link {
    display: block;
    margin-top: 10px;
    font-size: 1em;
}

.ppseries-advertisement-right-sidebar .advertisement-box-header,
.ppseries-content-promo-box .advertisement-box-header {
    background: #655897;
    color: #ffffff;
}

.ppseries-advertisement-right-sidebar .advertisement-box-content,
.ppseries-content-promo-box.advertisement-box-content {
    border: 1px solid #655897;
}


.ppseries-content-promo-box .upgrade-btn {
    margin-top: 20px;
}

.ppseries-content-promo-box .upgrade-btn a {
    background: #FCB223;
    color: #000 !important;
    font-weight: normal;
    text-decoration: none;
    padding: 9px 12px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    box-sizing: border-box;
    border: 1px solid #fca871;
    break-inside: avoid;
    white-space: nowrap;
}

.ppseries-content-promo-box .upgrade-btn a:hover {
    background: #fcca46;
    color: #000 !important;
}

.ppseries-content-promo-box.postbox.upgrade-pro .inside-content {
    position: relative;
    margin: 11px 0;
    padding: 0 12px 12px;
    line-height: 1.4;
    font-size: 13px;
}

@media only screen and (min-width: 1075px) {
    .ppseries-advertisement-right-sidebar-message,
    .upgrade-btn {
        display: inline-block;
    }

    .ppseries-advertisement-right-sidebar-message {
        margin-right: 25px;
    }
}

@media only screen and (max-width: 1074px) {
    .ppseries-advertisement-right-sidebar-message,
    .upgrade-btn {
        display: block;
    }

    .upgrade-btn {
        margin-top: 20px;
    }

    .upgrade-btn a {
        max-width: 170px;
    }
}

/* Mobile styles */
@media only screen and (max-width: 782px) {
    .ppseries-settings-tab-content .form-table th {
        width: auto !important;
    }
}