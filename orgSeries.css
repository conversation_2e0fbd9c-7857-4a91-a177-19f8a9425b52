.imgset {
	float: left;
	/*overflow: visible;*/
	border: none;
	margin-right: 6px;
}

.serieslist-content {
	font-size: 1.2em;
	color: #000;
	text-align: left;
}

.serieslist-box {
	background: #fff;
	width: 90%;
	/*height: 100px;*/
	padding: 6px;
	margin: 10px auto 0 auto;
	border: 1px solid #1E3A96;
	border-bottom: 2px solid #1E3A96;
}

html>body .serieslist-box {
	height: auto;
	/*min-height: 100px;*/
}

h2.series-title {
	font-size: 1.2em;
	color: #1E3A96;
	text-align: center;
}

ul.serieslist-ul {
	margin: 0;
	padding: 0;
	list-style-type: none;
}

li.serieslist-li {
	margin: 0;
	padding: 0;
}

.seriesbox {
	background: #F8F8F8;
	color: #777;
	text-align: left;
	font-size: .8em;
	margin: 0 10px 0px 10px;
	padding: 0 20px 20px;
	border: 1px solid #1E3A96;
	border-bottom-width: 3px;
	min-width: 125px;
}

.seriesbox img {
	padding-top: 20px;
}

h3.series-title-post-page {
	font-size: 1.1em;
	color: #1E3A96;
	text-align: center;
}

p.series-description {
	font-style: italic;
}

.seriesmeta {
	background-color: #E5F3FF;
	font-size: .8em;
	padding: 20px;
	margin-bottom: 20px;
}

.series-nav-left {
	float: left;
    margin-right: 20px;
}

.series-nav-right {
	float: right;
}

.stocpagination {
	clear: both;
	padding: 20px 0;
	position: relative;
	left: 465px;
	font-size: 12px;
	line-height: 13px;
	color: #fff;
}

.stocpagination a {
   display: block;
	text-decoration: none;
	color: #717171;
	font: bold 11px Arial, sans-serif;
	text-shadow: 0px 1px white;
	padding: 5px 8px;

	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;

	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.35);
	-moz-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.35);
	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.35);
	background: #f9f9f9;

	background: -webkit-linear-gradient(top, #f9f9f9 0%, #e8e8e8 100%);
	background: -moz-linear-gradient(top, #f9f9f9 0%, #e8e8e8 100%);
	background: -o-linear-gradient(top, #f9f9f9 0%, #e8e8e8 100%);
	background: -ms-linear-gradient(top, #f9f9f9 0%, #e8e8e8 100%);
	background: linear-gradient(top, #f9f9f9 0%, #e8e8e8 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f9f9f9', endColorstr='#e8e8e8',GradientType=0 );
}

.stocpagination a:visited {
   color: #5F7BB3;
   background-color: #F1F6F5;
   text-decoration: none;
}

.page-numbers {
	background-color:transparent;
	display: block;
	float: left;
	margin: 0 5px 5px 0;
	padding: 6px 9px 5px 9px;
	text-decoration:none;
	width: auto;
	color: #fff;
	background: #555;
	border-color: #c8d6d6 #9EADAD #9EADAD #c8d6d6;
}

.stocpagination a:hover {
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.55);
	-moz-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.55);
	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.55);
	background: #fff;
	background: -webkit-linear-gradient(top, #fff 0%, #e8e8e8 100%);
	background: -moz-linear-gradient(top, #fff 0%, #e8e8e8 100%);
	background: -o-linear-gradient(top, #fff 0%, #e8e8e8 100%);
	background: -ms-linear-gradient(top, #fff 0%, #e8e8e8 100%);
	background: linear-gradient(top, #fff 0%, #e8e8e8 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fff', endColorstr='#e8e8e8',GradientType=0 );
}

.stocpagination a:active, .page-numbers.current:active {
	-webkit-box-shadow: inset 0px 1px 3px 0px rgba(0,0,0,0.5), 0px 1px 1px 0px rgba(255,255,255,1);
	-moz-box-shadow: inset 0px 1px 3px 0px rgba(0,0,0,0.5), 0px 1px 1px 0px rgba(255,255,255,1);
	box-shadow: inset 0px 1px 3px 0px rgba(0,0,0,0.5), 0px 1px 1px 0px rgba(255,255,255,1);
}

.page-numbers.current:hover {
	-webkit-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.9);
	-moz-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.9);
	box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.9);
	background: #99cefc;
	background: -webkit-linear-gradient(top, #99cefc 0%, #57a1d8 100%);
	background: -moz-linear-gradient(top, #99cefc 0%, #57a1d8 100%);
	background: -o-linear-gradient(top, #99cefc 0%, #57a1d8 100%);
	background: -ms-linear-gradient(top, #99cefc 0%, #57a1d8 100%);
	background: linear-gradient(top, #99cefc 0%, #57a1d8 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#99cefc', endColorstr='#57a1d8',GradientType=0 );
}

.page-numbers.current {
	color: white;
	text-shadow: 0px 1px #3f789f;
	-webkit-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.8);
	-moz-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.8);
	box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.8);
	background: #7cb9e5;
	background: -webkit-linear-gradient(top, #7cb9e5 0%, #57a1d8 100%);
	background: -moz-linear-gradient(top, #7cb9e5 0%, #57a1d8 100%);
	background: -o-linear-gradient(top, #7cb9e5 0%, #57a1d8 100%);
	background: -ms-linear-gradient(top, #7cb9e5 0%, #57a1d8 100%);
	background: linear-gradient(top, #7cb9e5 0%, #57a1d8 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#7cb9e5', endColorstr='#57a1d8',GradientType=0 );
}
