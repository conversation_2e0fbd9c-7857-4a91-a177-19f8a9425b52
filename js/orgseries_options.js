jQuery(document).ready(function($) {
	// DEBUG: Log that the script is loading
	console.log('🔍 DEBUG: PublishPress Series options script loaded!');
	alert('🔍 DEBUG: PublishPress Series options script loaded! Check console for more details.');

	//Thickbox
    $(document).on('click','input[name="option_reset"]', function(){
        tb_show('Are you sure you want to Reset?','TB_inline?height=155&amp;width=300&amp;inlineId=TBcontent');
        return false;
    });

    $('input#TBcancel').click(function(){
        tb_remove();
    });

    $('input#TBsubmit').click(function(){
		$('input.reset_option', '#series_options' ).val('1');
        document.series_options.submit();
    });

    $('#auto_tag_toggle').click(function(e) {
      var is_checked = $('input#auto_tag_toggle').is(':checked');

      if (is_checked) {
        $(".series_post_list_limit_row").show();
      }
  
      if (!is_checked) {
        $(".series_post_list_limit_row").hide();
      }
    });

    $('#custom_css').click(function(e) {
      var is_checked = $('input#custom_css').is(':checked');
  
      if (is_checked) {
        $("input[class='css_style']").attr('disabled', false);
      }
  
      if (!is_checked) {
        $("input[class='css_style']").attr('disabled', true);
      }
    });

    // Overview page tab settings
    $('#series_overview_page_layout').on('change', function(){
        // Show / Hide columns field
        if($(this).val() == 'grid'){
            $('.ppseries-settings-table').find('tr.pps-row-columns').show();
        } else {
            $('.ppseries-settings-table').find('tr.pps-row-columns').hide();
        }
        // Show / Hide layout description
        if($(this).val() != 'default'){
            $('#series_overview_page_layout_desc').show();
        } else {
            $('#series_overview_page_layout_desc').hide();
        }
	});

    // Show / Hide layout description on load
    if($('#series_overview_page_layout').val() == 'default'){
        $('#series_overview_page_layout_desc').hide();
    }

    // Template Preview functionality
    var previewTimeout;
    var previewCache = {};

    // Debug: Log when script loads
    console.log('PublishPress Series: Template preview script loaded');
    console.log('Found template inputs:', $('.ppseries-template-input').length);

    // Initialize template previews on page load
    $('.ppseries-template-input').each(function() {
        console.log('Initializing preview for:', $(this).attr('id'));
        updateTemplatePreview($(this));
    });

    // Listen for template input changes
    $('.ppseries-template-input').on('input keyup paste', function() {
        var $input = $(this);

        // Clear existing timeout
        clearTimeout(previewTimeout);

        // Set new timeout for debounced update
        previewTimeout = setTimeout(function() {
            updateTemplatePreview($input);
        }, 500); // 500ms delay
    });

    function updateTemplatePreview($input) {
        var templateContent = $input.val();
        var previewTarget = $input.data('preview-target');
        var $previewContainer = $('#' + previewTarget);
        var $previewContent = $previewContainer.find('.ppseries-template-preview-content');
        var $loadingIndicator = $previewContainer.find('.ppseries-template-preview-loading');
        var templateType = $previewContent.data('template-type');

        console.log('Updating preview for:', templateType, 'Target:', previewTarget);
        console.log('Preview container found:', $previewContainer.length);
        console.log('Preview content found:', $previewContent.length);

        // Check cache first
        var cacheKey = templateType + '_' + btoa(templateContent).substring(0, 50);
        if (previewCache[cacheKey]) {
            $previewContent.html(previewCache[cacheKey]);
            return;
        }

        // Show loading indicator
        $loadingIndicator.show();

        // Make AJAX request
        console.log('Making AJAX request with data:', {
            action: 'ppseries_template_preview',
            template_type: templateType,
            template_content: templateContent.substring(0, 100) + '...',
            nonce: ppseriesPreview.nonce
        });

        $.ajax({
            url: ppseriesPreview.ajaxurl || ajaxurl,
            type: 'POST',
            data: {
                action: 'ppseries_template_preview',
                template_type: templateType,
                template_content: templateContent,
                nonce: ppseriesPreview.nonce
            },
            success: function(response) {
                if (response.success) {
                    var previewHtml = response.data;
                    $previewContent.html(previewHtml);

                    // Cache the result
                    previewCache[cacheKey] = previewHtml;
                } else {
                    $previewContent.html('<div style="color: #d63638; font-style: italic;">Preview error: ' + (response.data || 'Unknown error') + '</div>');
                }
            },
            error: function() {
                $previewContent.html('<div style="color: #d63638; font-style: italic;">Preview unavailable</div>');
            },
            complete: function() {
                $loadingIndicator.hide();
            }
        });
    }
});