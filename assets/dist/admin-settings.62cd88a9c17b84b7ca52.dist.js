webpackJsonp([0],{"0iPh":function(e,t){e.exports=jQuery},1:function(e,t,n){n("c3EJ"),e.exports=n("8baS")},"8baS":function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}var i=n("p2OF"),a=o(i),s=n("0iPh"),c=o(s),r=n("Aoin"),u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(r),l=n("c+Sx"),d="activation",f="deactivation";(0,a.default)(function(){function e(e,t,a,s){var r=(0,c.default)(".os-license-key-meta",e);s.data.content&&r.html(s.data.content),s.data.success&&n(t,e),s.data.notices&&o(s.data.notices,e),s.data.nonce&&i(s.data.nonce,a)}function t(e,t,n){var i="";n.response?i=void 0!==n.response.data.content?n.response.data.content:"There was a problem with the ajax request. Returned status of: "+response.status:n.request?(console.log(n.request),i=n.request.response):i=n.message,o((0,l.noticeBuilder)("error",i,!1),e)}function n(e,t){u.config.debug&&console.log(e);var n=(0,c.default)(".js-license-submit",t),o=d===e?d+"-button":f+"-button",i=d===e?f+"-button":d+"-button",a=d===e?"os_license_key_deactivate":"os_license_key_activate",s=d===e?u.i18n.deactivateButtonText:u.i18n.activateButtonText;n.removeClass(o).addClass(i).attr("name",a).val(s)}function o(e,t){if("string"!=typeof e)throw u.config.debug&&console.log(e),new TypeError("Invalid notices type. Expected a string.");(0,c.default)(".os-license-notices",t).html(e)}function i(e,t){t.val(e)}(0,c.default)(".os-license-key-container").on("click",".js-license-submit",function(n){n.preventDefault(),n.stopPropagation();var o=(0,c.default)(this).data(),i=(0,c.default)(this).hasClass("activation-button")?d:f,a=(0,c.default)("#os_license_key_nonce_"+o.extension),s={action:"os_license_key_"+i,nonce:a.val(),license_key:(0,c.default)("#os-license-key-"+o.extension).val(),extension:o.extension},r="#os-license-key-container-"+o.extension,l=(0,c.default)(r),p=r+" .spinner";u.config.debug&&console.log(s),u.toggleAjaxSpinner(p),u.ajax(s,!0,function(t){u.toggleAjaxSpinner(p),e(l,i,a,t)},function(e){u.toggleAjaxSpinner(p),t(l,i,e)})})})},Aoin:function(e,t){e.exports=osjs},"c+Sx":function(e,t,n){"use strict";function o(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=n?" is-dismissible":"";if("string"!=typeof t)throw new TypeError("Incoming {message} variable should be a string.");if("string"!=typeof e)throw new TypeError("Incoming {type} variable should be a string.");return'<div class="notice '+e+o+'"><p>'+t+"</p></div>"}Object.defineProperty(t,"__esModule",{value:!0}),t.noticeBuilder=o},c3EJ:function(e,t,n){"use strict"},p2OF:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(e){if("complete"===document.readyState||"interactive"===document.readyState)return e();document.addEventListener("DOMContentLoaded",e)};t.default=o}},[1]);
//# sourceMappingURL=admin-settings.62cd88a9c17b84b7ca52.dist.js.map