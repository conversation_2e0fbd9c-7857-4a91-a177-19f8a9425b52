{"version": 3, "sources": ["webpack:///admin-settings.62cd88a9c17b84b7ca52.dist.js", "webpack:///external \"jQuery\"", "webpack:///./assets/src/license-management.js", "webpack:///external \"osjs\"", "webpack:///./assets/src/modules/wp-notice-builder.js", "webpack:///./node_modules/@wordpress/dom-ready/build-module/index.js"], "names": ["webpackJsonp", "0iPh", "module", "exports", "j<PERSON><PERSON><PERSON>", "1", "__webpack_require__", "8baS", "_interopRequireDefault", "obj", "__esModule", "default", "_dom<PERSON><PERSON>y", "_domReady2", "_j<PERSON>y", "_jquery2", "_osjs", "os", "newObj", "key", "Object", "prototype", "hasOwnProperty", "call", "_wpNoticeBuilder", "ACTION_ACTIVATION", "ACTION_DEACTIVATION", "handleLicenseKeySuccess", "container", "activationAction", "nonceField", "response", "licenseMetaContainer", "data", "content", "html", "success", "switchButton", "notices", "showNotices", "nonce", "replaceNonce", "handleLicenseKeyError", "error", "message", "status", "request", "console", "log", "noticeBuilder", "config", "debug", "currentButton", "classReplaced", "classAdded", "buttonNameAttr", "buttonText", "i18n", "deactivateButtonText", "activateButtonText", "removeClass", "addClass", "attr", "val", "TypeError", "on", "e", "preventDefault", "stopPropagation", "buttonData", "this", "hasClass", "extension", "action", "license_key", "containerSelector", "spinnerSelector", "toggleAjaxSpinner", "ajax", "<PERSON><PERSON><PERSON>", "osjs", "c+Sx", "type", "dismissible", "arguments", "length", "undefined", "isDismissible", "defineProperty", "value", "c3EJ", "p2OF", "__webpack_exports__", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callback", "document", "readyState", "addEventListener"], "mappings": "AAAAA,cAAc,IAERC,OACA,SAAUC,EAAQC,GCHxBD,EAAAC,QAAAC,QDSMC,EACA,SAAUH,EAAQC,EAASG,GAEjCA,EAAoB,QACpBJ,EAAOC,QAAUG,EAAoB,SAK/BC,OACA,SAAUL,EAAQC,EAASG,GAEjC,YAmBA,SAASE,GAAuBC,GAAO,MAAOA,IAAOA,EAAIC,WAAaD,GAAQE,QAASF,GErCvF,GAAAG,GAAAN,EAAA,QFuBIO,EAAaL,EAAuBI,GElBxCE,EAAAR,EAAA,QFsBIS,EAAWP,EAAuBM,GErBtCE,EAAAV,EAAA,QAAYW,EF6BZ,SAAiCR,GAAO,GAAIA,GAAOA,EAAIC,WAAc,MAAOD,EAAc,IAAIS,KAAa,IAAW,MAAPT,EAAe,IAAK,GAAIU,KAAOV,GAAWW,OAAOC,UAAUC,eAAeC,KAAKd,EAAKU,KAAMD,EAAOC,GAAOV,EAAIU,GAAgC,OAAtBD,GAAOP,QAAUF,EAAYS,GAJjOF,GExBjCQ,EAAAlB,EAAA,QAEMmB,EAAoB,aACpBC,EAAsB,gBAG5B,EAAAb,EAAAF,SAAS,WAyCL,QAASgB,GAAwBC,EAAWC,EAAkBC,EAAYC,GACtE,GAAIC,IAAuB,EAAAjB,EAAAJ,SAAE,uBAAwBiB,EACjDG,GAASE,KAAKC,SACdF,EAAqBG,KAAKJ,EAASE,KAAKC,SAExCH,EAASE,KAAKG,SACdC,EAAaR,EAAkBD,GAE/BG,EAASE,KAAKK,SACdC,EAAYR,EAASE,KAAKK,QAASV,GAEnCG,EAASE,KAAKO,OACdC,EAAaV,EAASE,KAAKO,MAAOV,GAU1C,QAASY,GAAsBd,EAAWC,EAAkBc,GACxD,GAAIC,GAAU,EAEVD,GAAMZ,SACNa,MAAiD,KAAhCD,EAAMZ,SAASE,KAAKC,QAC/BS,EAAMZ,SAASE,KAAKC,QACpB,kEAAoEH,SAASc,OAC5EF,EAAMG,SACbC,QAAQC,IAAIL,EAAMG,SAClBF,EAAUD,EAAMG,QAAQf,UAExBa,EAAUD,EAAMC,QAEpBL,GAAY,EAAAf,EAAAyB,eAXD,QAWqBL,GAAS,GAAQhB,GASrD,QAASS,GAAaR,EAAkBD,GAChCX,EAAGiC,OAAOC,OACVJ,QAAQC,IAAInB,EAEhB,IAAIuB,IAAgB,EAAArC,EAAAJ,SAAE,qBAAsBiB,GACxCyB,EAAgB5B,IAAsBI,EAChCJ,EAAoB,UACpBC,EAAsB,UAC5B4B,EAAa7B,IAAsBI,EAC7BH,EAAsB,UACtBD,EAAoB,UAC1B8B,EAAiB9B,IAAsBI,EACjC,4BACA,0BACN2B,EAAa/B,IAAsBI,EAC7BZ,EAAGwC,KAAKC,qBACRzC,EAAGwC,KAAKE,kBAClBP,GAAcQ,YAAYP,GAAeQ,SAASP,GAAYQ,KAAK,OAAQP,GAAgBQ,IAAIP,GASnG,QAASjB,GAAYD,EAASV,GAE1B,GAAuB,gBAAZU,GAIP,KAHIrB,GAAGiC,OAAOC,OACVJ,QAAQC,IAAIV,GAEV,GAAI0B,WAAU,6CAKxB,EAAAjD,EAAAJ,SA5HwB,sBA4HGiB,GAAWO,KAAKG,GAS/C,QAASG,GAAaD,EAAOV,GACzBA,EAAWiC,IAAIvB,IAnInB,EAAAzB,EAAAJ,SAAE,6BAA6BsD,GAAG,QAAS,qBAAsB,SAASC,GACtEA,EAAEC,iBACFD,EAAEE,iBACF,IAAIC,IAAa,EAAAtD,EAAAJ,SAAE2D,MAAMrC,OACrBJ,GAAmB,EAAAd,EAAAJ,SAAE2D,MAAMC,SAAS,qBAAuB9C,EAAoBC,EAC/EI,GAAa,EAAAf,EAAAJ,SAAE,yBAA2B0D,EAAWG,WACrDvC,GACIwC,OAAS,kBAAoB5C,EAC7BW,MAAQV,EAAWiC,MACnBW,aAAc,EAAA3D,EAAAJ,SAAE,mBAAqB0D,EAAWG,WAAWT,MAC3DS,UAAYH,EAAWG,WAE3BG,EAAoB,6BAA+BN,EAAWG,UAC9D5C,GAAY,EAAAb,EAAAJ,SAAEgE,GACdC,EAAkBD,EAAoB,WACtC1D,GAAGiC,OAAOC,OACVJ,QAAQC,IAAIf,GAEhBhB,EAAG4D,kBAAkBD,GACjB3D,EAAG6D,KACC7C,GACA,EACA,SAASF,GACLd,EAAG4D,kBAAkBD,GACrBjD,EAAwBC,EAAWC,EAAkBC,EAAYC,IAErE,SAASY,GACL1B,EAAG4D,kBAAkBD,GACrBlC,EAAsBd,EAAWC,EAAkBc,UFgIjEoC,KACA,SAAU7E,EAAQC,GG9KxBD,EAAAC,QAAA6E,MHoLMC,OACA,SAAU/E,EAAQC,EAASG,GAEjC,YI/KO,SAAS2C,GAAciC,EAAMtC,GAA8B,GAArBuC,GAAqBC,UAAAC,OAAA,OAAAC,KAAAF,UAAA,IAAAA,UAAA,GAC1DG,EAAgBJ,EAAc,kBAAoB,EACtD,IAAuB,gBAAZvC,GACP,KAAM,IAAIoB,WAAU,kDAExB,IAAoB,gBAATkB,GACP,KAAM,IAAIlB,WAAU,+CAExB,OAAO,sBAAwBkB,EAAOK,EAAgB,QAAU3C,EAAU,aJiL9ExB,OAAOoE,eAAerF,EAAS,cAC3BsF,OAAO,IAEXtF,EI5LgB8C,iBJ4MVyC,KACA,SAAUxF,EAAQC,EAASG,GAEjC,cAKMqF,KACA,SAAUzF,EAAQ0F,EAAqBtF,GAE7C,YK/NAc,QAAAoE,eAAAI,EAAA,cAAAH,OAAA,GAOA,IAAAI,GAAA,SAAAC,GACA,gBAAAC,SAAAC,YACA,gBAAAD,SAAAC,WAEA,MAAAF,IAIAC,UAAAE,iBAAA,mBAAAH,GAGAF,GAAA,aLsOG", "file": "admin-settings.62cd88a9c17b84b7ca52.dist.js", "sourcesContent": ["webpackJsonp([0],{\n\n/***/ \"0iPh\":\n/***/ (function(module, exports) {\n\nmodule.exports = jQuery;\n\n/***/ }),\n\n/***/ 1:\n/***/ (function(module, exports, __webpack_require__) {\n\n__webpack_require__(\"c3EJ\");\nmodule.exports = __webpack_require__(\"8baS\");\n\n\n/***/ }),\n\n/***/ \"8baS\":\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\nvar _domReady = __webpack_require__(\"p2OF\");\n\nvar _domReady2 = _interopRequireDefault(_domReady);\n\nvar _jquery = __webpack_require__(\"0iPh\");\n\nvar _jquery2 = _interopRequireDefault(_jquery);\n\nvar _osjs = __webpack_require__(\"Aoin\");\n\nvar os = _interopRequireWildcard(_osjs);\n\nvar _wpNoticeBuilder = __webpack_require__(\"c+Sx\");\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * Internal Imports\n */\nvar ACTION_ACTIVATION = 'activation';\n\n/**\n * External Imports.\n */\n\nvar ACTION_DEACTIVATION = 'deactivation';\nvar CONTAINER_NOTICES_CLASS = '.os-license-notices';\n\n(0, _domReady2.default)(function () {\n    (0, _jquery2.default)('.os-license-key-container').on('click', '.js-license-submit', function (e) {\n        e.preventDefault();\n        e.stopPropagation();\n        var buttonData = (0, _jquery2.default)(this).data(),\n            activationAction = (0, _jquery2.default)(this).hasClass('activation-button') ? ACTION_ACTIVATION : ACTION_DEACTIVATION,\n            nonceField = (0, _jquery2.default)('#os_license_key_nonce_' + buttonData.extension),\n            data = {\n            action: 'os_license_key_' + activationAction,\n            nonce: nonceField.val(),\n            license_key: (0, _jquery2.default)('#os-license-key-' + buttonData.extension).val(),\n            extension: buttonData.extension\n        },\n            containerSelector = '#os-license-key-container-' + buttonData.extension,\n            container = (0, _jquery2.default)(containerSelector),\n            spinnerSelector = containerSelector + ' ' + '.spinner';\n        if (os.config.debug) {\n            console.log(data);\n        }\n        os.toggleAjaxSpinner(spinnerSelector);\n        os.ajax(data, true, function (response) {\n            os.toggleAjaxSpinner(spinnerSelector);\n            handleLicenseKeySuccess(container, activationAction, nonceField, response);\n        }, function (error) {\n            os.toggleAjaxSpinner(spinnerSelector);\n            handleLicenseKeyError(container, activationAction, error);\n        });\n    });\n\n    /**\n     * Handles the ajax success for license key activation/deactivation.\n     * @param {Object} container  jQuery container object for where the html content will be output.\n     * @param {String} activationAction  What type of activation this is (activation or deactivation).\n     * @param {Object} nonceField jQuery container object for the nonce field\n     * @param {Object} response   @see axios response schema.\n     */\n    function handleLicenseKeySuccess(container, activationAction, nonceField, response) {\n        var licenseMetaContainer = (0, _jquery2.default)('.os-license-key-meta', container);\n        if (response.data.content) {\n            licenseMetaContainer.html(response.data.content);\n        }\n        if (response.data.success) {\n            switchButton(activationAction, container);\n        }\n        if (response.data.notices) {\n            showNotices(response.data.notices, container);\n        }\n        if (response.data.nonce) {\n            replaceNonce(response.data.nonce, nonceField);\n        }\n    }\n\n    /**\n     * Handles the ajax fail for license key activation/deactivation.\n     * @param {Object} container  jQuery container object for where the html content will be output.\n     * @param {String} activationAction\n     * @param {Object} error   @see axios error schema.\n     */\n    function handleLicenseKeyError(container, activationAction, error) {\n        var message = '',\n            type = 'error';\n        if (error.response) {\n            message = typeof error.response.data.content !== 'undefined' ? error.response.data.content : 'There was a problem with the ajax request. Returned status of: ' + response.status;\n        } else if (error.request) {\n            console.log(error.request);\n            message = error.request.response;\n        } else {\n            message = error.message;\n        }\n        showNotices((0, _wpNoticeBuilder.noticeBuilder)(type, message, false), container);\n    }\n\n    /**\n     * Switch all the elements for the activation/deactivation button.\n     * @param {string} activationAction\n     * @param {Object} container  jQuery selector for the container containing the button.\n     */\n    function switchButton(activationAction, container) {\n        if (os.config.debug) {\n            console.log(activationAction);\n        }\n        var currentButton = (0, _jquery2.default)('.js-license-submit', container),\n            classReplaced = ACTION_ACTIVATION === activationAction ? ACTION_ACTIVATION + '-button' : ACTION_DEACTIVATION + '-button',\n            classAdded = ACTION_ACTIVATION === activationAction ? ACTION_DEACTIVATION + '-button' : ACTION_ACTIVATION + '-button',\n            buttonNameAttr = ACTION_ACTIVATION === activationAction ? 'os_license_key_deactivate' : 'os_license_key_activate',\n            buttonText = ACTION_ACTIVATION === activationAction ? os.i18n.deactivateButtonText : os.i18n.activateButtonText;\n        currentButton.removeClass(classReplaced).addClass(classAdded).attr('name', buttonNameAttr).val(buttonText);\n    }\n\n    /**\n     * Adds the incoming notices (expected html string) to the notices container.\n     * @param notices\n     * @param container\n     */\n    function showNotices(notices, container) {\n        if (typeof notices !== 'string') {\n            if (os.config.debug) {\n                console.log(notices);\n            }\n            throw new TypeError('Invalid notices type. Expected a string.');\n        }\n        /** @todo left off here trying to figure out why this isn't showing the notices!)\n         *  - also need to figure out why our global error handler in os-common.js isn't working as expected.\n         */\n        (0, _jquery2.default)(CONTAINER_NOTICES_CLASS, container).html(notices);\n    }\n\n    /**\n     * Replaces the nonce for this license key update with the given value.\n     * @param {String} nonce       New nonce.\n     * @param {Object} nonceField  jQuery container for the nonce field\n     */\n    function replaceNonce(nonce, nonceField) {\n        nonceField.val(nonce);\n    }\n});\n\n/***/ }),\n\n/***/ \"Aoin\":\n/***/ (function(module, exports) {\n\nmodule.exports = osjs;\n\n/***/ }),\n\n/***/ \"c+Sx\":\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n/**\n * Builds a notice container\n * @param {String} type\n * @param {String} message\n * @param {Boolean} dismissible\n * @return {String}\n */\n\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nexports.noticeBuilder = noticeBuilder;\nfunction noticeBuilder(type, message) {\n    var dismissible = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n    var isDismissible = dismissible ? ' is-dismissible' : '';\n    if (typeof message !== 'string') {\n        throw new TypeError('Incoming {message} variable should be a string.');\n    }\n    if (typeof type !== 'string') {\n        throw new TypeError('Incoming {type} variable should be a string.');\n    }\n    return '<div class=\"notice ' + type + isDismissible + '\"><p>' + message + '</p></div>';\n}\n\n/***/ }),\n\n/***/ \"c3EJ\":\n/***/ (function(module, exports, __webpack_require__) {\n\n\"use strict\";\n\n\n/***/ }),\n\n/***/ \"p2OF\":\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\nObject.defineProperty(__webpack_exports__, \"__esModule\", { value: true });\n/**\n * Specify a function to execute when the DOM is fully loaded.\n *\n * @param {Function} callback A function to execute after the DOM is ready.\n *\n * @return {void}\n */\nvar domReady = function domReady(callback) {\n  if (document.readyState === 'complete' || // DOMContentLoaded + Images/Styles/etc loaded, so we call directly.\n  document.readyState === 'interactive' // DOMContentLoaded fires at this point, so we call directly.\n  ) {\n      return callback();\n    } // DOMContentLoaded has not fired yet, delay callback until then.\n\n\n  document.addEventListener('DOMContentLoaded', callback);\n};\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (domReady);\n//# sourceMappingURL=index.js.map\n\n/***/ })\n\n},[1]);\n\n\n// WEBPACK FOOTER //\n// admin-settings.62cd88a9c17b84b7ca52.dist.js", "module.exports = jQuery;\n\n\n//////////////////\n// WEBPACK FOOTER\n// external \"jQuery\"\n// module id = 0iPh\n// module chunks = 0", "/**\n * Internal Imports\n */\nimport domReady from '@wordpress/dom-ready';\n\n/**\n * External Imports.\n */\nimport $ from 'jquery';\nimport * as os from 'osjs';\nimport {noticeBuilder} from \"./modules/wp-notice-builder\";\n\nconst ACTION_ACTIVATION = 'activation';\nconst ACTION_DEACTIVATION = 'deactivation';\nconst CONTAINER_NOTICES_CLASS = '.os-license-notices';\n\ndomReady(function(){\n    $('.os-license-key-container').on('click', '.js-license-submit', function(e){\n        e.preventDefault();\n        e.stopPropagation();\n        let buttonData = $(this).data(),\n            activationAction = $(this).hasClass('activation-button') ? ACTION_ACTIVATION : ACTION_DEACTIVATION,\n            nonceField = $('#os_license_key_nonce_' + buttonData.extension),\n            data = {\n                action : 'os_license_key_' + activationAction,\n                nonce : nonceField.val(),\n                license_key : $('#os-license-key-' + buttonData.extension).val(),\n                extension : buttonData.extension\n            },\n            containerSelector = '#os-license-key-container-' + buttonData.extension,\n            container = $(containerSelector),\n            spinnerSelector = containerSelector + ' ' + '.spinner';\n        if (os.config.debug) {\n            console.log(data);\n        }\n        os.toggleAjaxSpinner(spinnerSelector);\n            os.ajax(\n                data,\n                true,\n                function(response) {\n                    os.toggleAjaxSpinner(spinnerSelector);\n                    handleLicenseKeySuccess(container, activationAction, nonceField, response);\n                },\n                function(error) {\n                    os.toggleAjaxSpinner(spinnerSelector);\n                    handleLicenseKeyError(container, activationAction, error);\n                }\n            );\n    });\n\n    /**\n     * Handles the ajax success for license key activation/deactivation.\n     * @param {Object} container  jQuery container object for where the html content will be output.\n     * @param {String} activationAction  What type of activation this is (activation or deactivation).\n     * @param {Object} nonceField jQuery container object for the nonce field\n     * @param {Object} response   @see axios response schema.\n     */\n    function handleLicenseKeySuccess(container, activationAction, nonceField, response) {\n        let licenseMetaContainer = $('.os-license-key-meta', container);\n        if (response.data.content) {\n            licenseMetaContainer.html(response.data.content);\n        }\n        if (response.data.success) {\n            switchButton(activationAction, container);\n        }\n        if (response.data.notices) {\n            showNotices(response.data.notices, container);\n        }\n        if (response.data.nonce) {\n            replaceNonce(response.data.nonce, nonceField);\n        }\n    }\n\n    /**\n     * Handles the ajax fail for license key activation/deactivation.\n     * @param {Object} container  jQuery container object for where the html content will be output.\n     * @param {String} activationAction\n     * @param {Object} error   @see axios error schema.\n     */\n    function handleLicenseKeyError(container, activationAction, error) {\n        let message = '',\n            type = 'error';\n        if (error.response) {\n            message = typeof error.response.data.content !== 'undefined'\n                ? error.response.data.content\n                : 'There was a problem with the ajax request. Returned status of: ' + response.status;\n        } else if (error.request) {\n            console.log(error.request);\n            message = error.request.response;\n        } else {\n            message = error.message;\n        }\n        showNotices(noticeBuilder(type, message, false), container);\n    }\n\n\n    /**\n     * Switch all the elements for the activation/deactivation button.\n     * @param {string} activationAction\n     * @param {Object} container  jQuery selector for the container containing the button.\n     */\n    function switchButton(activationAction, container) {\n        if (os.config.debug) {\n            console.log(activationAction);\n        }\n        let currentButton = $('.js-license-submit', container),\n            classReplaced = ACTION_ACTIVATION === activationAction\n                ? ACTION_ACTIVATION + '-button'\n                : ACTION_DEACTIVATION + '-button',\n            classAdded = ACTION_ACTIVATION === activationAction\n                ? ACTION_DEACTIVATION + '-button'\n                : ACTION_ACTIVATION + '-button',\n            buttonNameAttr = ACTION_ACTIVATION === activationAction\n                ? 'os_license_key_deactivate'\n                : 'os_license_key_activate',\n            buttonText = ACTION_ACTIVATION === activationAction\n                ? os.i18n.deactivateButtonText\n                : os.i18n.activateButtonText;\n        currentButton.removeClass(classReplaced).addClass(classAdded).attr('name', buttonNameAttr).val(buttonText);\n    }\n\n\n    /**\n     * Adds the incoming notices (expected html string) to the notices container.\n     * @param notices\n     * @param container\n     */\n    function showNotices(notices, container)\n    {\n        if (typeof notices !== 'string') {\n            if (os.config.debug) {\n                console.log(notices);\n            }\n            throw new TypeError('Invalid notices type. Expected a string.');\n        }\n        /** @todo left off here trying to figure out why this isn't showing the notices!)\n         *  - also need to figure out why our global error handler in os-common.js isn't working as expected.\n         */\n        $(CONTAINER_NOTICES_CLASS, container).html(notices);\n    }\n\n\n    /**\n     * Replaces the nonce for this license key update with the given value.\n     * @param {String} nonce       New nonce.\n     * @param {Object} nonceField  jQuery container for the nonce field\n     */\n    function replaceNonce(nonce, nonceField) {\n        nonceField.val(nonce);\n    }\n});\n\n\n// WEBPACK FOOTER //\n// ./assets/src/license-management.js", "module.exports = osjs;\n\n\n//////////////////\n// WEBPACK FOOTER\n// external \"osjs\"\n// module id = Aoin\n// module chunks = 0", "\"use strict\";\n/**\n * Builds a notice container\n * @param {String} type\n * @param {String} message\n * @param {Boolean} dismissible\n * @return {String}\n */\nexport function noticeBuilder(type, message, dismissible = false) {\n    let isDismissible = dismissible ? ' is-dismissible' : '';\n    if (typeof message !== 'string') {\n        throw new TypeError('Incoming {message} variable should be a string.');\n    }\n    if (typeof type !== 'string') {\n        throw new TypeError('Incoming {type} variable should be a string.')\n    }\n    return '<div class=\"notice ' + type + isDismissible + '\"><p>' + message + '</p></div>';\n}\n\n\n// WEBPACK FOOTER //\n// ./assets/src/modules/wp-notice-builder.js", "/**\n * Specify a function to execute when the DOM is fully loaded.\n *\n * @param {Function} callback A function to execute after the DOM is ready.\n *\n * @return {void}\n */\nvar domReady = function domReady(callback) {\n  if (document.readyState === 'complete' || // DOMContentLoaded + Images/Styles/etc loaded, so we call directly.\n  document.readyState === 'interactive' // DOMContentLoaded fires at this point, so we call directly.\n  ) {\n      return callback();\n    } // DOMContentLoaded has not fired yet, delay callback until then.\n\n\n  document.addEventListener('DOMContentLoaded', callback);\n};\n\nexport default domReady;\n//# sourceMappingURL=index.js.map\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./node_modules/@wordpress/dom-ready/build-module/index.js\n// module id = p2OF\n// module chunks = 0"], "sourceRoot": ""}