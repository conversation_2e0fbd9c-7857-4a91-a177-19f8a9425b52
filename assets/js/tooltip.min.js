(function(global){const PP_TooltipsLibrary={init(){this.applyTooltips(document);this.bindGlobalClick()},applyTooltips(context=document){const tooltips=context.querySelectorAll('[data-toggle="tooltip"].pp-tooltips-library');tooltips.forEach(tooltip=>{if(tooltip.dataset.tooltipProcessed==="true")return;tooltip.dataset.tooltipProcessed="true";const tooltipBox=tooltip.querySelector(".tooltip-text");if(!tooltipBox)return;if(!tooltipBox.querySelector("i")){const arrow=document.createElement("i");tooltipBox.appendChild(arrow)}if(tooltip.classList.contains("click")){tooltip.addEventListener("click",e=>{e.preventDefault();e.stopPropagation();tooltip.classList.toggle("is-active")})}})},bindGlobalClick(){document.addEventListener("click",function(event){const tooltip=event.target.closest('[data-toggle="tooltip"].pp-tooltips-library.click');if(tooltip){event.preventDefault();event.stopPropagation();tooltip.classList.toggle("is-active")}})},refresh(context=document){this.applyTooltips(context)}};document.addEventListener("DOMContentLoaded",()=>PP_TooltipsLibrary.init());global.PP_TooltipsLibrary=PP_TooltipsLibrary})(window);