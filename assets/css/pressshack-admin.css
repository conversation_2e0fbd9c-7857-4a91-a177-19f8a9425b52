/**
 * @package PublishPress
 * <AUTHOR>
 *
 * Copyright (c) 2018 PublishPress
 *
 * ------------------------------------------------------------------------------
 * Based on Edit Flow
 * Author: <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and
 * others
 * Copyright (c) 2009-2016 <PERSON>, <PERSON>, et al.
 * ------------------------------------------------------------------------------
 *
 * This file is part of PublishPress
 *
 * PublishPress is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * PublishPress is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with PublishPress.  If not, see <http://www.gnu.org/licenses/>.
 */

.pressshack-admin-wrapper a:link,
.pressshack-admin-wrapper a:visited,
.pressshack-admin-wrapper a:active,
.pressshack-admin-wrapper a:hover {
    text-decoration: none;
}

.pressshack-admin-wrapper a,
.pressshack-admin-wrapper button,
.pressshack-admin-wrapper button::before {
    -webkit-transition: all 200ms ease-in-out;
    -moz-transition: all 200ms ease-in-out;
    -o-transition: all 200ms ease-in-out;
    transition: all 200ms ease-in-out;
}

.pressshack-admin-wrapper a,
.pressshack-admin-wrapper a div,
.pressshack-admin-wrapper a p {
    color: #655997;
}

.pressshack-admin-wrapper a:hover,
.pressshack-admin-wrapper a:focus,
.pressshack-admin-wrapper a:active,
.pressshack-admin-wrapper a:hover div,
.pressshack-admin-wrapper a:focus div,
.pressshack-admin-wrapper a:active div,
.pressshack-admin-wrapper a:hover p,
.pressshack-admin-wrapper a:focus p,
.pressshack-admin-wrapper a:active p,
.pressshack-admin-wrapper a:hover .dashicons:before,
.pressshack-admin-wrapper a:focus .dashicons:before,
.pressshack-admin-wrapper a:active .dashicons:before {
    color: #5A4F87;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    -o-box-shadow: none;
}

.pressshack-admin-wrapper > header h1 a,
.pressshack-admin-wrapper > header h1 a:hover,
.pressshack-admin-wrapper > header h1 a:focus,
.pressshack-admin-wrapper > header h1 a:active {
    color: #23282d;
}

.pressshack-admin-wrapper .nav-tab-wrapper .nav-tab {
    color: inherit;
}

.pressshack-admin-wrapper .nav-tab-wrapper .nav-tab-active,
.pressshack-admin-wrapper .nav-tab-wrapper .nav-tab:hover,
.pressshack-admin-wrapper .nav-tab-wrapper .nav-tab:active,
.pressshack-admin-wrapper .nav-tab-wrapper .nav-tab:focus {
    border-top-color: #655997;
    color: #655997;
}

.pressshack-admin-wrapper > footer {
    text-align: center;
}

.pressshack-admin-wrapper > footer > div.pp-rating {
    font-size: 12px;
    margin-bottom: 10px;
    margin-top: 30px;
}

.pressshack-admin-wrapper > footer * {
    color: #777;
}

.pressshack-admin-wrapper > footer > nav ul {
    list-style: none;
}

.pressshack-admin-wrapper > footer > nav ul > li {
    display: inline-block;
}

.pressshack-admin-wrapper > footer > nav ul > li:not(:first-child) {
    margin-left: 15px;
}

.pressshack-admin-wrapper > footer > nav ul > li > a {
    font-weight: bold;
}

.pressshack-admin-wrapper > footer .dashicons.dashicons-star-filled {
    line-height: 18px;
    font-size: 12px;
    width: 12px;
    height: 12px;
    color: #FFB300;
    -webkit-transition: color 200ms ease-in-out;
    -moz-transition: color 200ms ease-in-out;
    -o-transition: color 200ms ease-in-out;
    transition: color 200ms ease-in-out;
}

.pressshack-admin-wrapper button:not(.notice-dismiss),
.pressshack-admin-wrapper .button:not(.notice-dismiss) {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    text-shadow: initial;
    -webkit-box-shadow: initial;
    -moz-box-shadow: initial;
    box-shadow: initial;
    vertical-align: middle;
    line-height: 0;
    min-height: 28px;
    text-decoration: none;
    padding: 15px 10px;
    border-width: 2px;
    border-style: solid;
}

.pressshack-admin-wrapper .button-primary {
    background-color: #FFB300;
    border-color: #C58C07;
    color: #754D26;
    
    text-shadow: none;
}

.pressshack-admin-wrapper .button-primary:hover,
.pressshack-admin-wrapper .button-primary:active,
.pressshack-admin-wrapper .button-primary:focus {
    background-color: #F3AC04;
    border-color: #C58C07;
    color: #333;
    outline: none;
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
    -o-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
}

.pressshack-admin-wrapper .button:not(.notice-dismiss):hover,
.pressshack-admin-wrapper .button:not(.notice-dismiss):active,
.pressshack-admin-wrapper .button:not(.notice-dismiss):focus {
    outline: none;
    -webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
    -moz-box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2) inset, 0 1px 2px rgba(0, 0, 0, 0.05);
}

.pressshack-admin-wrapper .pp-pressshack-logo img {
    width: 170px;
}

/**
 * Fremius tweaks
 */

#piframe,
.fs-secure-notice {
    display: none;
}

/**
 * Template Preview Styles
 */
.ppseries-template-preview-container {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
}

.ppseries-template-preview-header {
    background: #fff;
    border-bottom: 1px solid #ddd;
    padding: 10px 15px;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ppseries-template-preview-header strong {
    color: #23282d;
}

.ppseries-template-preview-loading {
    color: #666;
    font-style: italic;
}

.ppseries-template-preview-content {
    padding: 15px;
    min-height: 50px;
    background: #fff;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
}

/* Series Meta Preview Styles */
.ppseries-template-preview-content .seriesmeta {
    background: #f0f8ff;
    border-left: 4px solid #0073aa;
    padding: 12px 16px;
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #333;
}

.ppseries-template-preview-content .seriesmeta a {
    color: #0073aa;
    text-decoration: none;
    font-weight: 500;
}

.ppseries-template-preview-content .seriesmeta a:hover {
    color: #005a87;
    text-decoration: underline;
}

/* Series Post List Preview Styles */
.ppseries-template-preview-content .seriesbox {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.ppseries-template-preview-content .seriesbox .center {
    text-align: center;
    margin-bottom: 20px;
}

.ppseries-template-preview-content .seriesbox .center a {
    color: #0073aa;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
}

.ppseries-template-preview-content .serieslist-ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ppseries-template-preview-content .serieslist-li,
.ppseries-template-preview-content .serieslist-li-current {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.ppseries-template-preview-content .serieslist-li:last-child,
.ppseries-template-preview-content .serieslist-li-current:last-child {
    border-bottom: none;
}

.ppseries-template-preview-content .serieslist-li a {
    color: #0073aa;
    text-decoration: none;
}

.ppseries-template-preview-content .serieslist-li a:hover {
    color: #005a87;
    text-decoration: underline;
}

.ppseries-template-preview-content .serieslist-li-current {
    font-weight: 600;
    color: #333;
}

/* Series Navigation Preview Styles */
.ppseries-template-preview-content fieldset {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
    background: #fafafa;
}

.ppseries-template-preview-content fieldset legend {
    font-weight: 600;
    color: #333;
    padding: 0 10px;
}

.ppseries-template-preview-content .series-nav-left {
    float: left;
}

.ppseries-template-preview-content .series-nav-right {
    float: right;
}

.ppseries-template-preview-content .series-nav-left a,
.ppseries-template-preview-content .series-nav-right a {
    color: #0073aa;
    text-decoration: none;
    padding: 8px 12px;
    background: #f0f8ff;
    border-radius: 3px;
    display: inline-block;
}

.ppseries-template-preview-content .series-nav-left a:hover,
.ppseries-template-preview-content .series-nav-right a:hover {
    background: #e6f3ff;
    text-decoration: none;
}

/* Clear floats */
.ppseries-template-preview-content fieldset::after {
    content: "";
    display: table;
    clear: both;
}

/* Series Table of Contents Preview Styles */
.ppseries-template-preview-content .serieslist-box {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    background: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
}

.ppseries-template-preview-content .serieslist-box .imgset {
    float: left;
    margin-right: 20px;
    margin-bottom: 10px;
}

.ppseries-template-preview-content .serieslist-box .serieslist-content h2 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
}

.ppseries-template-preview-content .serieslist-box .serieslist-content h2 a {
    color: #0073aa;
    text-decoration: none;
}

.ppseries-template-preview-content .serieslist-box .serieslist-content h2 a:hover {
    color: #005a87;
    text-decoration: underline;
}

.ppseries-template-preview-content .serieslist-box .serieslist-content p {
    margin: 0;
    color: #666;
    line-height: 1.6;
}

.ppseries-template-preview-content .serieslist-box hr {
    clear: both;
    border: none;
    border-top: 1px solid #eee;
    margin: 20px 0 0 0;
}

/* Token placeholder styles */
.ppseries-preview-token {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 12px;
    color: #856404;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ppseries-template-preview-content .series-nav-left,
    .ppseries-template-preview-content .series-nav-right {
        float: none;
        display: block;
        margin-bottom: 10px;
    }
}
