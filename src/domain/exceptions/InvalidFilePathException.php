<?php
namespace OrganizeSeries\domain\exceptions;

use InvalidArgumentException;

/**
 * Class InvalidFilePathException
 * thrown when is_readable() returns false for a given filepath
 *
 * @package       OrganzieSeries\domain\exceptions
 * <AUTHOR>
 * @since         2.5.9
 */
class InvalidFilePathException extends InvalidArgumentException {

    /**
     * InvalidClassException constructor.
     *
     * @param string     $file_path
     * @param string     $message
     * @param int        $code
     * @param \Exception $previous
     */
    public function __construct( $file_path, $message = '', $code = 0, \Exception $previous = null ) {
        if ( empty( $message ) ) {
            $message = sprintf(
                __(
                    'The "%1$s" file is either missing or could not be read due to permissions. Please ensure that the following path is correct and verify that the file permissions are correct:%2$s %3$s',
                    'organize-series'
                ),
                basename( $file_path ),
                '<br />',
                $file_path
            );
        }
        parent::__construct( $message, $code, $previous );
    }

}
