/**
 * PublishPress Series Frontend Override CSS
 * High specificity styles to ensure plugin styles win over theme styles
 */

/* Series Box - High Specificity Overrides */
body .entry-content .seriesbox,
body .post-content .seriesbox,
body .content .seriesbox,
body article .seriesbox,
body main .seriesbox,
.seriesbox {
    background: #f8f8f8 !important;
    color: #777 !important;
    text-align: left !important;
    font-size: 0.8em !important;
    margin: 0 10px 20px 10px !important;
    padding: 0 20px 20px !important;
    border: 1px solid #1e3a96 !important;
    border-bottom: 3px solid #1e3a96 !important;
    border-radius: 0 !important;
    min-width: 125px !important;
    max-width: none !important;
    width: auto !important;
    box-sizing: border-box !important;
    display: block !important;
    clear: both !important;
    overflow: visible !important;
    box-shadow: none !important;
}

/* Series Box Center Title */
body .entry-content .seriesbox .center,
body .post-content .seriesbox .center,
body .content .seriesbox .center,
body article .seriesbox .center,
body main .seriesbox .center,
.seriesbox .center {
    text-align: center !important;
    padding: 10px 0 !important;
    margin: 0 !important;
}

body .entry-content .seriesbox .center a,
body .post-content .seriesbox .center a,
body .content .seriesbox .center a,
body article .seriesbox .center a,
body main .seriesbox .center a,
.seriesbox .center a {
    font-size: 1.1em !important;
    color: #1e3a96 !important;
    text-decoration: none !important;
    font-weight: bold !important;
    display: inline !important;
}

/* Series List UL */
body .entry-content .seriesbox ul.serieslist-ul,
body .post-content .seriesbox ul.serieslist-ul,
body .content .seriesbox ul.serieslist-ul,
body article .seriesbox ul.serieslist-ul,
body main .seriesbox ul.serieslist-ul,
.seriesbox ul.serieslist-ul {
    margin: 0 !important;
    padding: 0 !important;
    list-style-type: none !important;
    list-style: none !important;
    border: none !important;
    background: none !important;
}

/* Series List LI */
body .entry-content .seriesbox ul.serieslist-ul li,
body .post-content .seriesbox ul.serieslist-ul li,
body .content .seriesbox ul.serieslist-ul li,
body article .seriesbox ul.serieslist-ul li,
body main .seriesbox ul.serieslist-ul li,
.seriesbox ul.serieslist-ul li {
    margin: 0 !important;
    padding: 5px 0 !important;
    list-style: none !important;
    list-style-type: none !important;
    border: none !important;
    background: none !important;
    position: relative !important;
}

/* Series List Links */
body .entry-content .seriesbox ul.serieslist-ul li a,
body .post-content .seriesbox ul.serieslist-ul li a,
body .content .seriesbox ul.serieslist-ul li a,
body article .seriesbox ul.serieslist-ul li a,
body main .seriesbox ul.serieslist-ul li a,
.seriesbox ul.serieslist-ul li a {
    color: #777 !important;
    text-decoration: underline !important;
    font-size: inherit !important;
    font-weight: normal !important;
    display: inline !important;
    border: none !important;
    background: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

body .entry-content .seriesbox ul.serieslist-ul li a:hover,
body .post-content .seriesbox ul.serieslist-ul li a:hover,
body .content .seriesbox ul.serieslist-ul li a:hover,
body article .seriesbox ul.serieslist-ul li a:hover,
body main .seriesbox ul.serieslist-ul li a:hover,
.seriesbox ul.serieslist-ul li a:hover {
    color: #1e3a96 !important;
    text-decoration: underline !important;
}

/* Series Meta - High Specificity Overrides */
body .entry-content .seriesmeta,
body .post-content .seriesmeta,
body .content .seriesmeta,
body article .seriesmeta,
body main .seriesmeta,
.seriesmeta {
    background-color: #e5f3ff !important;
    font-size: 0.8em !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    border: none !important;
    border-left: 4px solid #1e3a96 !important;
    color: #333 !important;
    display: block !important;
    clear: both !important;
    box-sizing: border-box !important;
}

body .entry-content .seriesmeta a,
body .post-content .seriesmeta a,
body .content .seriesmeta a,
body article .seriesmeta a,
body main .seriesmeta a,
.seriesmeta a {
    color: #1e3a96 !important;
    text-decoration: none !important;
    font-weight: 500 !important;
}

body .entry-content .seriesmeta a:hover,
body .post-content .seriesmeta a:hover,
body .content .seriesmeta a:hover,
body article .seriesmeta a:hover,
body main .seriesmeta a:hover,
.seriesmeta a:hover {
    color: #0f1d4b !important;
    text-decoration: underline !important;
}

/* Series Navigation */
body .entry-content .series-nav-left,
body .post-content .series-nav-left,
body .content .series-nav-left,
body article .series-nav-left,
body main .series-nav-left,
.series-nav-left {
    float: left !important;
    margin-right: 20px !important;
    margin-bottom: 10px !important;
}

body .entry-content .series-nav-right,
body .post-content .series-nav-right,
body .content .series-nav-right,
body article .series-nav-right,
body main .series-nav-right,
.series-nav-right {
    float: right !important;
    margin-left: 20px !important;
    margin-bottom: 10px !important;
}

/* Clear floats after series elements */
body .entry-content .seriesbox::after,
body .post-content .seriesbox::after,
body .content .seriesbox::after,
body article .seriesbox::after,
body main .seriesbox::after,
.seriesbox::after {
    content: "" !important;
    display: table !important;
    clear: both !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    body .entry-content .series-nav-left,
    body .post-content .series-nav-left,
    body .content .series-nav-left,
    body article .series-nav-left,
    body main .series-nav-left,
    .series-nav-left,
    body .entry-content .series-nav-right,
    body .post-content .series-nav-right,
    body .content .series-nav-right,
    body article .series-nav-right,
    body main .series-nav-right,
    .series-nav-right {
        float: none !important;
        display: block !important;
        margin: 0 0 10px 0 !important;
    }
    
    body .entry-content .seriesbox,
    body .post-content .seriesbox,
    body .content .seriesbox,
    body article .seriesbox,
    body main .seriesbox,
    .seriesbox {
        margin: 0 0 20px 0 !important;
        min-width: auto !important;
    }
}

/* Override common theme resets */
body .entry-content .seriesbox *,
body .post-content .seriesbox *,
body .content .seriesbox *,
body article .seriesbox *,
body main .seriesbox *,
.seriesbox * {
    box-sizing: border-box !important;
}

/* Ensure proper display for series elements */
body .entry-content .seriesbox,
body .post-content .seriesbox,
body .content .seriesbox,
body article .seriesbox,
body main .seriesbox,
.seriesbox,
body .entry-content .seriesmeta,
body .post-content .seriesmeta,
body .content .seriesmeta,
body article .seriesmeta,
body main .seriesmeta,
.seriesmeta {
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
}
