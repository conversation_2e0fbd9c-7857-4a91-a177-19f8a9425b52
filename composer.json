{"name": "publishpress/publishpress-series-pro", "description": "A WordPress plugin for managing serial content.", "keywords": ["wordpress", "plugin", "organize-series"], "homepage": "https://publishpress.com/publishpress-series/", "type": "wordpress-plugin", "license": "GPLv2", "authors": [{"name": "PublishPress", "email": "<EMAIL>", "homepage": "https://publishpress.com/"}], "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=7.2.5"}, "require-dev": {"automattic/vipwpcs": "^2.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.1"}, "scripts": {"build": "ppbuild build", "build:dir": "ppbuild build-dir", "build:clean": "ppbuild clean", "get-version": "ppbuild version", "check:longpath": "longpath .", "pre-autoload-dump": "composer dumpautoload --working-dir=./lib", "pre-update-cmd": "composer update --working-dir=./lib", "pre-install-cmd": "composer install --working-dir=./lib"}, "extra": {"plugin-name": "publishpress-series-pro", "plugin-slug": "publishpress-series-pro", "plugin-folder": "publishpress-series-pro"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}}