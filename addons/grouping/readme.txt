=== PublishPress Series Addon - Grouping ===
Contributors: nerrad
Tags: series, groups
Requires at least: 3.7
Tested up to: 4.8
Stable tag: 2.2.3
License: GPLv2
License URI: http://www.gnu.org/licenses/gpl-2.0.html

This addon gives you the ability to group series together by category.

== Description ==
One of the common case uses I've heard from people requesting this feature is the following:  Say you are using your blog to write a book over a bunch of posts.  The posts in each series are chapters in a book (chapter 1 is a post in a book).  This way people can easily navigate through the different chapters.  What if, however, the book is actually part of a series of books.  In this case you'd want a way to present the Books as a part of a group.  Aha!  This is where PublishPress Series Grouping would come in to solve the problem.

== Installation ==
This add-on requires PublishPress Series Core to be installed and active.

1. MAKE SURE YOU BACKUP YOUR WORDPRESS DATABASE (that\'s all in caps for a reason - nothing *should* go wrong but it\'s a good precaution nevertheless!!)
1. Download the File (or use the built-in updater provided by WordPress)
1. Extract to a folder in `../wp-content/plugins/`. The add-on folder can be named whatever you want but the default is \"organize-series-grouping\".  The final structure would be something like this: `../wp-content/plugins/organize-series-grouping/--and all the plugin files/folders--`
1. Activate the plugin on your WordPress plugins page.

You can do the above or just use the new plugin install integrated in WordPress.

== Changelog ==
All change-log information for the plugin can be found at https://organizeseries.com/changelogs