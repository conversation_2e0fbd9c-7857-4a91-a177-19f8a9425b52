=== PublishPress Series Addon - Shortcodes ===
Contributors: nerrad
Tags: series, groups
Requires at least: 3.7
Tested up to: 4.8
Stable tag: 1.3.2
License: GPLv2
License URI: http://www.gnu.org/licenses/gpl-2.0.html

This addon enables the ability for users to easily add series information to posts (or pages) via the use of shortcodes (integrated with the WordPress shortcode API).

== Description ==
PublishPress Series Shortcodes provides a set of WordPress "shortcodes" that provide users a way to easily insert various series information into their posts (or pages!).  (You can learn more about WordPress shortcodes <a href="http://codex.wordpress.org/Shortcode">here at the WordPress Codex</a>).  To make it EVEN easier, this addon provides buttons for both the advanced mce editor on your "add/edit posts/pages" screen AND buttons for the HTML editor.  So, you don't even need to remember what the shortcodes are!

== Installation ==
This add-on requires PublishPress Series Core to be installed and active.

1. MAKE SURE YOU BACKUP YOUR WORDPRESS DATABASE (that\'s all in caps for a reason - nothing *should* go wrong but it\'s a good precaution nevertheless!!)
1. Download the File (or use the built-in updater provided by WordPress)
1. Extract to a folder in `../wp-content/plugins/`. The add-on folder can be named whatever you want but the default is \"organize-series-shortcodes\".  The final structure would be something like this: `../wp-content/plugins/organize-series-shortcodes/--and all the plugin files/folders--`
1. Activate the plugin on your WordPress plugins page.

You can do the above or just use the new plugin install integrated in WordPress.

== Changelog ==
All change-log information for the plugin can be found at https://organizeseries.com/changelogs