=== Publishpress Series Custom Post Type Support ===
Contributors: nerrad
Tags: series, custom post types
Requires at least: 3.7
Tested up to: 4.8
Stable tag: 0.1
License: GPLv2
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Adds support for enabling Publishpress Series usage with custom post types.

== Description ==
This plugin is an addon for the Publishpress Series plugin that gives custom post type support so you can use Publishpress Series with other WordPress post types.  After activating, visit the <a href="/wp-admin/options-general.php?page=orgseries_options_page" title="Series Options Page Link">Series Options Page</a> to select which custom post types you'd like series activated for.

== Installation ==
This add-on requires Publishpress Series Core to be installed and active.

1. MAKE SURE YOU BACKUP YOUR WORDPRESS DATABASE (that\'s all in caps for a reason - nothing *should* go wrong but it\'s a good precaution nevertheless!!)
1. Download the File (or use the built-in updater provided by WordPress)
1. Extract to a folder in `../wp-content/plugins/`. The orgSeries folder can be named whatever you want but the default is \"organize-series-cpt-support\".  The final structure would be something like this: `../wp-content/plugins/organize-series-cpt-support/--and all the plugin files/folders--`
1. Activate the plugin on your WordPress plugins page.

You can do the above or just use the new plugin install integrated in WordPress.

== Changelog ==
All change-log information for the plugin can be found at https://organizeseries.com/changelogs