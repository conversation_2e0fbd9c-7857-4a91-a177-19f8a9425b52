{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "8477e04549c688919d000a59855f1674", "packages": [{"name": "composer/installers", "version": "v1.0.6", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "b3bd071ea114a57212c75aa6a2eef5cfe0cc798f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/b3bd071ea114a57212c75aa6a2eef5cfe0cc798f", "reference": "b3bd071ea114a57212c75aa6a2eef5cfe0cc798f", "shasum": ""}, "replace": {"shama/baton": "*"}, "require-dev": {"composer/composer": "1.0.*@dev", "phpunit/phpunit": "3.7.*"}, "type": "composer-installer", "extra": {"class": "Composer\\Installers\\Installer", "branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Composer\\Installers\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama", "role": "Developer"}], "description": "A multi-framework Composer library installer", "homepage": "http://composer.github.com/installers/", "keywords": ["TYPO3 CMS", "TYPO3 Flow", "TYPO3 Neos", "agl", "cakephp", "codeigniter", "drupal", "fuelphp", "installer", "j<PERSON><PERSON>", "kohana", "laravel", "li3", "lithium", "mako", "modulework", "phpbb", "ppi", "silverstripe", "symfony", "wordpress", "zend"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/master"}, "time": "2013-08-20T04:37:09+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.6"}, "platform-dev": [], "plugin-api-version": "2.0.0"}