jQuery( function($) {

	let series_new_button_style = Number(seriesL10n.metabox_show_add_new) > 0 ? '' : 'display: none;';
	$('#jaxseries').prepend('<span id="series-multiples-add" style="' + series_new_button_style + '"><input type="text" name="newseries" id="newseries" size="16" autocomplete="off"/><input type="button" name="Button" class="add:serieschecklist:jaxseries button" id="seriesadd" value="' + seriesL10n.add + '" /><input type="hidden"/><input type="hidden"/></span><span id="series-ajax-response"></span><span id="add-series-nonce" class="hidden">' + seriesL10n.addnonce + '</span>')

	$('#jaxseries').on('click', '#seriesadd', function () {
		/* console.log($('#newseries').val()); /**/

		var data = {
			action: 'add_series',
			newseries: $('#newseries').val(),
			addnonce: $('#add-series-nonce').text()
		}
		$.post(ajaxurl, data, function(response) {
			var resp = $.parseJSON(response);
			if ( !resp.error ) {
				$('#newseries').val('');
				$('ul#serieschecklist li:first').after(resp.html);
				$('#series-'+resp.id).animate({backgroundColor: "transparent"}, 3000);
				$('#add-series-nonce').text(resp.new_nonce);
			} else {
				$('#series-ajax-response').html(resp.error);
			}
		});
	});

	// Handle "Not part of a series" checkbox behavior
	$(document).on('change', '#in-series-0', function() {
		if ($(this).is(':checked')) {
			// Uncheck all other series checkboxes
			$('#serieschecklist input[type="checkbox"]:not(#in-series-0)').prop('checked', false);
		}
	});

	// Handle other series checkboxes
	$(document).on('change', '#serieschecklist input[type="checkbox"]:not(#in-series-0)', function() {
		if ($(this).is(':checked')) {
			// Uncheck "Not part of a series" checkbox
			$('#in-series-0').prop('checked', false);
		} else {
			// If no other checkboxes are checked, check "Not part of a series"
			var anyChecked = $('#serieschecklist input[type="checkbox"]:not(#in-series-0):checked').length > 0;
			if (!anyChecked) {
				$('#in-series-0').prop('checked', true);
			}
		}
	});
});
