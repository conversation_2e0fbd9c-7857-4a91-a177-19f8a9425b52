<?php

// autoload-classmap.php @generated by Strauss

$strauss_src = dirname(__FILE__);

return array(
   'PublishPress\PimplePimple\Versions' => $strauss_src . '/Versions.php',
   'PublishPress\Pimple\Container' => $strauss_src . '/pimple/pimple/src/Pimple/Container.php',
   'PublishPress\Pimple\Tests\ServiceIteratorTest' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/ServiceIteratorTest.php',
   'PublishPress\Pimple\Tests\PimpleTest' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/PimpleTest.php',
   'PublishPress\Pimple\Tests\Psr11\ContainerTest' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/Psr11/ContainerTest.php',
   'PublishPress\Pimple\Tests\Psr11\ServiceLocatorTest' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/Psr11/ServiceLocatorTest.php',
   'PublishPress\Pimple\Tests\PimpleServiceProviderInterfaceTest' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/PimpleServiceProviderInterfaceTest.php',
   'PublishPress\Pimple\Tests\Fixtures\NonInvokable' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/Fixtures/NonInvokable.php',
   'PublishPress\Pimple\Tests\Fixtures\Service' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/Fixtures/Service.php',
   'PublishPress\Pimple\Tests\Fixtures\Invokable' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/Fixtures/Invokable.php',
   'PublishPress\Pimple\Tests\Fixtures\PimpleServiceProvider' => $strauss_src . '/pimple/pimple/src/Pimple/Tests/Fixtures/PimpleServiceProvider.php',
   'PublishPress\Pimple\Psr11\Container' => $strauss_src . '/pimple/pimple/src/Pimple/Psr11/Container.php',
   'PublishPress\Pimple\Psr11\ServiceLocator' => $strauss_src . '/pimple/pimple/src/Pimple/Psr11/ServiceLocator.php',
   'PublishPress\Pimple\ServiceProviderInterface' => $strauss_src . '/pimple/pimple/src/Pimple/ServiceProviderInterface.php',
   'PublishPress\Pimple\Exception\FrozenServiceException' => $strauss_src . '/pimple/pimple/src/Pimple/Exception/FrozenServiceException.php',
   'PublishPress\Pimple\Exception\UnknownIdentifierException' => $strauss_src . '/pimple/pimple/src/Pimple/Exception/UnknownIdentifierException.php',
   'PublishPress\Pimple\Exception\InvalidServiceIdentifierException' => $strauss_src . '/pimple/pimple/src/Pimple/Exception/InvalidServiceIdentifierException.php',
   'PublishPress\Pimple\Exception\ExpectedInvokableException' => $strauss_src . '/pimple/pimple/src/Pimple/Exception/ExpectedInvokableException.php',
   'PublishPress\Pimple\ServiceIterator' => $strauss_src . '/pimple/pimple/src/Pimple/ServiceIterator.php',
);