<?php

// autoload_files.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    '3b1e1688e889525de91ac2456aba9efd' => $vendorDir . '/publishpress/psr-container/lib/include.php',
    '24b27b1b9a32bf58eda571c3e5ae3480' => $vendorDir . '/publishpress/pimple-pimple/lib/include.php',
    'c92bf23a32412037ecdc51806b458c36' => $vendorDir . '/alledia/edd-sl-plugin-updater/EDD_SL_Plugin_Updater.php',
    '0078757fbd019a5f202f2be6585c3626' => $vendorDir . '/publishpress/wordpress-banners/BannersMain.php',
    '9c8ea886233d3b80c33afe505e877a57' => $vendorDir . '/publishpress/wordpress-edd-license/src/include.php',
    '41c664bd04a95c2d6a2f2a3e00f06593' => $vendorDir . '/publishpress/wordpress-reviews/ReviewsController.php',
    'a61bc28a742b9f9f2fd5ef4d2d1e2037' => $vendorDir . '/publishpress/wordpress-version-notices/src/include.php',
);
