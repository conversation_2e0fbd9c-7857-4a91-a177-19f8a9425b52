<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitPublishPressSeriesPro
{
    public static $files = array (
        '3b1e1688e889525de91ac2456aba9efd' => __DIR__ . '/..' . '/publishpress/psr-container/lib/include.php',
        '24b27b1b9a32bf58eda571c3e5ae3480' => __DIR__ . '/..' . '/publishpress/pimple-pimple/lib/include.php',
        'c92bf23a32412037ecdc51806b458c36' => __DIR__ . '/..' . '/alledia/edd-sl-plugin-updater/EDD_SL_Plugin_Updater.php',
        '0078757fbd019a5f202f2be6585c3626' => __DIR__ . '/..' . '/publishpress/wordpress-banners/BannersMain.php',
        '9c8ea886233d3b80c33afe505e877a57' => __DIR__ . '/..' . '/publishpress/wordpress-edd-license/src/include.php',
        '41c664bd04a95c2d6a2f2a3e00f06593' => __DIR__ . '/..' . '/publishpress/wordpress-reviews/ReviewsController.php',
        'a61bc28a742b9f9f2fd5ef4d2d1e2037' => __DIR__ . '/..' . '/publishpress/wordpress-version-notices/src/include.php',
    );

    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PublishPress\\WordPressEDDLicense\\' => 33,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PublishPress\\WordPressEDDLicense\\' => 
        array (
            0 => __DIR__ . '/..' . '/publishpress/wordpress-edd-license/src/classes',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitPublishPressSeriesPro::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitPublishPressSeriesPro::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitPublishPressSeriesPro::$classMap;

        }, null, ClassLoader::class);
    }
}
