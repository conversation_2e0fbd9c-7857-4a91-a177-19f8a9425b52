=== Issues and Series for Newspapers, Magazines, Publishers, Writers  ===
Contributors: publishpress, kevinB, ste<PERSON><PERSON><PERSON><PERSON>, ander<PERSON><PERSON><PERSON>, olatechpro
Author: publishpress
Tags: issue, series, magazine, newspaper, publication
Requires at least: 5.5
Requires PHP: 7.2.5
Tested up to: 6.7
Stable tag: 2.14.0
License: GPLv2
License URI: http://www.gnu.org/licenses/gpl-2.0.html

PublishPress Series is a publishing plugin that allows you to organize posts into issues or series. This is ideal for magazines, newspapers, writers, teachers, comic artists, or anyone who writes multiple posts on the same topic.

== Description ==

[PublishPress Series](https://publishpress.com/series) is a publishing plugin that allows you to organize posts into issues or series. This is ideal for magazines, newspapers, short-story writers, teachers, comic artists, or anyone who writes multiple posts on the same topic.

Newspapers, magazines and publishers can use PublishPress Series to create issues based on topics or time periods:

* Collect posts into monthly issues for a magazine.
* Group together newspaper articles on the same topic.
* Organize chapters into an overall story.

When you're writing a post, you can easily add a post to an existing series, or start a new series. You can also add a new post into the middle of an existing series, and the plugin will adjust the order.

## PublishPress Series Pro ##

> <strong>Upgrade to Series Pro</strong><br />
> This plugin is the free version of the Series Pro plugin. The Pro version of PublishPress Series supports custom post types, multiple series, extra tokens, shortcodes and more. <a href="https://publishpress.com/series"  title="Series Pro">Click here to purchase the best WordPress newspaper and magazine plugin now!</a>

## Easily assign posts to your series ##

When you're writing a post, you can easily add a post to an existing series, or start a new series. You can also add a new post into the middle of an existing series, and the plugin will adjust the order.

It's easy to keep track of what posts have been added to a series. You can also filter the post list by series giving you a quick way to see all the posts you’ve already added to the series.

[Click here to get started with PublishPress Series](https://publishpress.com/knowledge-base/installation-series/).

## Display options for your Series information ##

Right out-of-the-box, the Series meta box will be automatically added to posts that are a part of a series. You can modify how this meta box appears using the template for it in the Series Options Page. Plus, there are at least four different displays that your visitors may see in PublishPress Series.

[Click here to see more about displaying Series](https://publishpress.com/knowledge-base/displays-series/).

## Advanced layouts for PublishPress Series ##

You can easily create beautiful layouts with PublishPress Series. This is possible thanks to an integration with the PublishPress Blocks plugin. Using the Content Display block, you choose your series from the “Show content with these Series” box. You will then have access to dozens of layout options.

[Click here to see more about advanced layouts](https://publishpress.com/knowledge-base/blocks-series/).

## Widgets and Blocks to show your Series information ##

PublishPress Series plugin provides two widgets you can use to display your series: Series Table of Contents, and Latest Series. Both widgets are packed with features and can be dropped into your theme, or into the Gutenberg editor.

[Click here to see more about Series widgets](https://publishpress.com/knowledge-base/series-widgets/).

## Order, schedule, publish and unpublish posts in your series

The Publish Series feature is available in the Free and Pro versions of PublishPress Series. This feature allows you to quickly manage all posts linked to a Series. You can use this screen to re-order, schedule publish, and unpublish all the posts in a Series.

[Click here to see more about Publish Series screen](https://publishpress.com/knowledge-base/usage-publisher/).

## Series Pro: Custom post type support ##

Custom Post Type support is available in the Pro version of PublishPress Series. With this feature, not only can you use PublishPress Series with WordPress posts, plus you can also create series with pages, or any other WordPress Custom Post Type. Your series can even have parts across different post types. That means you could have “Part 1” that is a post, “Part 2” that is a page, and “Part 3” that is another post type.

[Click here to see more about custom post types](https://publishpress.com/knowledge-base/custom-post-types/).

## Series Pro: Shortcodes ##

PublishPress Series Shortcodes is a feature in the Pro version of PublishPress Series. This provides a set of WordPress shortcodes that allow you insert series information into posts. There are five shortcodes available:

[Click here to see more about Series shortcodes](https://publishpress.com/knowledge-base/overview-shortcodes/).

## Series Pro: Multiples

The Pro version of PublishPress Series allows you to add posts to more than one series. The core plugin only allows posts to be added to one series. The most obvious change you will see after enabling this feature is inside the post editing screen. The “Series” metabox will allow you to choose more than one series.

[Click here to see more about the Multiples feature](https://publishpress.com/knowledge-base/overview-multiples/).

## Series Pro: Extra Tokens

 token is a specific format for indicating where you want series information to appear. Tokens can be added in any of the PublishPress Series templates available on the Series Options screen. Extra tokens are available in the Pro version of PublishPress Series. This adds new tokens to the “Templates” screen for customizing the various series templates.

[Click here to see more about the extra tokens](https://publishpress.com/knowledge-base/overview-tokens/).


= Join PublishPress and get the Pro plugins =

The Pro versions of the PublishPress plugins are well worth your investment. The Pro versions have extra features and faster support. [Click here to join PublishPress](https://publishpress.com/pricing/).

Join PublishPress and you'll get access to these Pro plugins:

* [PublishPress Authors Pro](https://publishpress.com/authors) allows you to add multiple authors and guest authors to WordPress posts.
* [PublishPress Blocks Pro](https://publishpress.com/blocks) has everything you need to build professional websites with the WordPress block editor.
* [PublishPress Capabilities Pro](https://publishpress.com/capabilities) is the plugin to manage your WordPress user roles, permissions, and capabilities.
* [PublishPress Checklists Pro](https://publishpress.com/checklists) enables you to define tasks that must be completed before content is published.
* [PublishPress Permissions Pro](https://publishpress.com/permissions)  is the plugin for advanced WordPress permissions.
* [PublishPress Pro](https://publishpress.com/publishpress) is the plugin for managing and scheduling WordPress content.
* [PublishPress Revisions Pro](https://publishpress.com/revisions) allows you to update your published pages with teamwork and precision.
* [PublishPress Series Pro](https://publishpress.com/series) enables you to group content together into a series 

Together, these plugins are a suite of powerful publishing tools for WordPress. If you need to create a professional workflow in WordPress, with moderation, revisions, permissions and more... then you should try PublishPress.

=  Bug Reports =

Bug reports for PublishPress Series are welcomed in our [repository on GitHub](https://github.com/publishpress/publishpress-series). Please note that GitHub is not a support forum, and that issues that aren't properly qualified as bugs will be closed.

= Follow the PublishPress team = 

Follow PublishPress on [Facebook](https://www.facebook.com/publishpress), [Twitter](https://www.twitter.com/publishpresscom) and [YouTube](https://www.youtube.com/publishpress).


== Installation ==

### INSTALL PUBLISHPRESS SERIES FROM WITHIN WORDPRESS

* Visit the plugins page within your dashboard and select ‘Add New’;
* Search for ‘PublishPress Series’;
* Activate PublishPress Series from your Plugins page.

### INSTALL PUBLISHPRESS SERIES MANUALLY

* Upload the ‘organize-series’ folder to the /wp-content/plugins/ directory;
* Activate the PublishPress Series plugin through the ‘Plugins’ menu in WordPress;


== Frequently Asked Questions ==

**What does PublishPress Series do?**

It makes it easier for you to write a series of posts on your blog/website and have them automatically linked together. One of the best ways to build an audience on your blog/website is to write short posts about the same subject over a period of time rather than one long post.  There are numerous SEO benefits to this as well.  PublishPress Series takes the hassle out of managing the serial posts and you can just focus on writing.  Another way of saying this is, PublishPress Series takes the work out of organizing series so that you write more and your readers can EASILY follow along.

**Can I customize the archive page for series?**

You sure can! Create a file called `taxonomy-series.php` and copy the code from your themes `archive.php` file and use that as the basis for building out how the series archive page looks.  You'll also want to take a look at all the template tags you can use for series in the `orgSeries-template-tags.php` file.

**Can I customize the archive page for a single series??**

Yes. Create a file named `taxonomy-series-{series-slug}.php` file and replace `{series-slug}` with the slug of your series and you'll be good to go!

**Can I create a page that lists all the series available?**

Yes, you can In the `organize-series` folder you'll see a file called `seriestoc.php`.  It's what gets loaded when you go to your series toc url (defaults at \series-toc\ but you can change this on the series options page).  Chances are though it doesn't look great with your theme (the file included just gives you an idea of what you can put in the file). What you want to do is copy this file to your theme folder and then modify it to match your theme structure (see your themes archive page for an example).  You don't need to put any of the WordPress loop code in this file.

** Does PublishPress Series use the WordPress taxonomy system? **

Yes, Publishpress Series introduces a new taxonomy "series". Thanks to the rich API provided to plugin authors by WordPress, PublishPress Series takes advantage of the WordPress core. 

== Screenshots ==


1. When you're writing a post, you can easily add a post to an existing series, or start a new series.

2. Integration with the Posts List table - it's easy to keep track of what posts have been added to a series.  

3. Right out-of-the-box, the Series meta box will be automatically added to posts that are a part of a series. 

4. Add, delete, edit all your series on one handy dandy page. You can also upload/select images to associate with the series.  

5. This feature is in the Pro version of PublishPress Series and it allows you to use the plugin with any WordPress post type.

6. The Pro version of PublishPress Series provides a set of WordPress “shortcodes” that provide users a way to easily insert various series information into their posts.

7. This feature is in the Pro version of PublishPress Series and it brings the capability of adding a post to more than one series.

8. The Pro version provides additional tokens to use in the series options page for customizing various series templates.

9. The PublishPress Series core plugin groups posts together in series. The Pro version gives the ability to put series together in groups.

10. The Pro version of Series enables easy bulk publishing of all posts in a series at once.


== Changelog ==

v2.14.0- 2025-07-14
* Added: New Promo for Pro version #901
* Fixed: Function _load_textdomain_just_in_time #900
* Fixed: Warning when SiteOrigin active #866
* Fixed: Hide the metabox if there are no Series #902
* Fixed: Hide Filter on CPT #875

v2.13.0- 2025-02-19
* Fixed: WordPress database error on install, #831
* Fixed: Undefined variable $current_post_id, #877
* Fixed: PHP Warning Undefined array key "post_series", #865
* Fixed: Deprecated message on EDD Vendor, #878
* Update: Update WordPress Reviews library to 1.1.20, 860

v2.12.0- 2023-08-17
* Changed: Replaced Pimple library with a prefixed version of the library to avoid conflicts with other plugins;
* Changed: Replaced Psr/Container library with a prefixed version of the library to avoid conflicts with other plugins;
* Changed: Change min PHP version to 7.2.5. If not compatible, the plugin will not execute;
* Changed: Change min WP version to 5.5. If not compatible, the plugin will not execute;
* Changed: Updated internal libraries to latest versions;
* Changed: Refactor some occurrences of "plugins_loaded" replacing it by a new action: "plublishpress_<name>_loaded" which runs after the requirements and libraries are loaded, but before the plugin is initialized;

v2.11.5- 2023-07-20
* Update: Allow users to re-order drafts series, #815
* Update: Add series metabox order settings, #814
* Update: Add series upgrade action button to settings, #810
* Fixed: Scheduled posts publish with no part, #821
* Update: Small text change for migration, #816
* Update: French translation update for Series Free - June 2023, #812
* Update: ES-IT Translation updates June 2023, #809
* Update: Series PRO ES-FR-IT Translation Updates July 2023, #825

v2.11.4- 2023-06-15
* Update: Remove auto series part feature
* Fixed: Series Order is broken with Multiple Series, #774
* Fixed: Editing Series order not updating, #746
* Fixed: Navigation is broken by draft posts, #783
* Update: Remove unpublished series parts in frontend, #782
* Fixed: Text in missing for Multiple Series, #773
* Fixed: Add a link to Manage Series if no series are available, #728
* Fixed: Remove the banner for Blocks, #770

v2.11.3- 2023-05-30
* Fixed: Multiple Series is not working in Pro, #769
* Fixed: "Post Types" no longer appears in Series Pro, #768
* Fixed: plugins_loaded hooks are not executed anymore, #771

v2.11.2- 2023-05-23
* Update: Fix issue with v2.10.1 and re-release update with below changes:
* Update: Added new token %post_title_short% and %post_title_short_linked%, #523
* Fixed: %post_title_list_short% not working, #523
* Fixed: Broken Post list display for series box style, #691
* Fixed: Classic Widget "Series Table of Content" sorting not working, #715
* Fixed: Taxonomy change not working, #701
* Fixed: Invalid form control with name=’series_part[0]’ is focusable when series part is disabled, #694
* Fixed: PHP Warning error in server log, #693
* Update: Add 'category_ids' and 'series_ids' parameters to [series_post_categories], #709
* Fixed: Sorting series is not working for [series_toc] shortcode, #696
* Fixed: Series Order not working for multiple series, #706
* Fixed: Manual series part not working when Automatic Numbering is enabled, #702
* Update: Series-v2.10.0-ES-IT_TranslationUpdate-5_October2022, #665

v2.11.1- 2023-05-18
* Update: Rollback 2.11.0

v2.11.0 - 2023-05-18
* Changed: Replaced Pimple library with a prefixed version of the library to avoid conflicts with other plugins;
* Changed: Replaced Psr/Container library with a prefixed version of the library to avoid conflicts with other plugins;
* Changed: Change min PHP version to 7.2.5. If not compatible, the plugin will not execute;
* Changed: Change min WP version to 5.5. If not compatible, the plugin will not execute;
* Changed: Updated internal libraries to latest versions;

v2.10.1- 2023-01-04
* Update: Added new token %post_title_short% and %post_title_short_linked%, #523
* Fixed: %post_title_list_short% not working, #523
* Fixed: Broken Post list display for series box style, #691
* Fixed: Classic Widget "Series Table of Content" sorting not working, #715
* Fixed: Taxonomy change not working, #701
* Fixed: Invalid form control with name=’series_part[0]’ is focusable when series part is disabled, #694
* Fixed: PHP Warning error in server log, #693
* Update: Add 'category_ids' and 'series_ids' parameters to [series_post_categories], #709
* Fixed: Sorting series is not working for [series_toc] shortcode, #696
* Fixed: Series Order not working for multiple series, #706
* Fixed: Manual series part not working when Automatic Numbering is enabled, #702
* Update: Series-v2.10.0-ES-IT_TranslationUpdate-5_October2022, #665

v2.10.0- 2022-10-04
* Feature: Add Series and Series Group re-order field with term drag and drop feature #584
* Feature: Add series order link to series and new post selected series #612
* Feature: Add series Groups(Categories) overview page shortcode [publishpress_series_categories] #591
* Feature: Add search box to series metabox #239
* Feature: Add settings to limit series meta display to single page only #588
* Fixed: Only allow valid numbers in series part input #634
* Update: Set series part if empty irrespective of Automatic Numbering settings #636
* Feature: Add filter, search and pagination to "Publish Series" screen #510
* Update: Enable "Show "Series Part"" by default #617
* Fixed: Font styles and font sizes inconsistency on 'Series Table of Contents' widget page #604
* Fixed: Syntax Error on 'Edit Series' page #607
* Update: Simplify the Publish Series screen #614
* Update: Change the menu links inside Series #615
* Fixed: TOC template header and footer deprecated in FSE themes #623
* Fixed: Groups template header and footer deprecated in FSE themes #416
* Fixed: Series taxonomy template header and footer deprecated in FSE themes #409
* Feature: Add "Series Groups" into a Free feature #613
* Fixed: Remove new taxonomy metabox #595
* Fixed: Float left for next post navigation #586
* Update: Series-Translation updates for Spanish July 21 #578
* Update: Series-Translation updates for French-Spanish-Italian #567
* Update: Remove "Create as unpublished"? #655
* Fixed: Series link in "Posts" table doesn't go anywhere #651

v2.9.2- 2022-07-12
* Fixed: Problem with series navigation and List #568
* Fixed: Define plugin addon path from primary plugin file to fix include error #570

v2.9.1- 2022-07-06
* Feature: Added series Table of Content shortcode [publishpress_series_toc], #559
* Update: Disable automatic numbering for new installs #515
* Fixed: %post_date% doesn't work #525
* Fixed: Exclude trash post from publish series "Unpublish all" #527
* Fixed: Small cleanup for "Publish Series" box #497
* Added: New Free vs Pro library #529
* Fixed: Multiple "No Series" in Quick Edit when no series is available #538
* Fixed: Issue with "Update Order" when posts are unpublished #528
* Added: Add a Promo for PublishPress Blocks #541
* Fixed: Issue with scheduled posts and part #552
* Update: Show "Post status" instead of "Last Modified" on the publish series screen #553
* Update: Updated translations #560
* Update: Translation check #557
* Fixed: Error in PHP 5.6.39 #535

v2.9.0.1- 2022-05-17
* Fixed: Series not enable in rest api #531

v2.9.0- 2022-04-21
* Fixed: Better way to re-order posts in a series #377
* Fixed: Improved publish Series display #494
* Fixed: Include a sidebar for Pro #462
* Fixed: Small warning about plain permalinks #496
* Fixed: Update "Series Groups" Menu title #495
* Fixed: Remove publish series menu promo from series menu #498
* Fixed: Re-numbering of posts within the series #477
* Fixed: Publish Series screen in Free version #478

v2.8.2- 2022-04-12
* Fixed: Issue with series menu permission #488
* Fixed: Remove lines and paddings from Series Metabox #484

v2.8.1- 2022-04-11
* Fixed: Custom Post Type Archive not working anymore #474
* Fixed: Issue with Post Title in Widget #473
* Fixed: Small changes to "Display" settings #466
* Fixed: "Series" should be "Settings" #470
* Fixed: Two issues with Series Table of Contents #465

v2.8.0- 2022-03-15
* Fixed: New theme with better design #272
* Fixed: Improved design for "Publish Series" screen #436
* Fixed: Improved design for Series overview page #309
* Fixed: Using "id="primary" for series overview page messes up with primary widget area #353
* Fixed: Improve design for Series taxonomy #395
* Fixed: PHP 8 issue in settings #400
* Fixed: Latest Series widget text #402
* Fixed: New area for Series Table of Contents #403
* Fixed: Series Meta (with excerpts): #404
* Fixed: Small text cleanup in "Display" #406
* Fixed: Improved design for Groups page #394
* Fixed: Move the "Overview" tab #405
* Fixed: Need spacing in Navigation template #418
* Fixed: Styling not working for group and toc pages #421
* Fixed: Simplify the metabox #408
* Fixed: Series Post List Post Title (unpublished) Template: #401
* Fixed: Issue with "the_content" hook #306
* Fixed: Move series order to overview section #424
* Fixed: Disable Series Free when Series Pro is installed #440
* Fixed: "Publish Series" feature only works with "Pending Review" #437
* Fixed: Using "Publish posts in series" produces a 0 for part number #454
* Fixed: Scheduled posts marked with no Part #452
* Fixed: Consistent design for Settings #434
* Fixed: Move series "Add New" metabox to html #433

v2.7.5- 2022-02-07
* Fixed: Stop automatic re-numbering of posts within the series #376
* Fixed: WPDebug shows a bunch of errors in the Settings #381
* Fixed: Notice: Undefined index: series_custom_base #380
* Fixed: Problem with Series Post List box and %series_part% #382

v2.7.4- 2022-01-20
* Fixed: Series option update message

v2.7.3- 2022-01-20
* Fixed: HTML tags being stripped when saving #362
* Fixed: PHP Warning: Undefined array key #354
* Fixed: Design for boxes is missing #352
* Fixed: Issue with translation string #366
* Fixed: Updated Italian, Spanish and French translations #361
* Fixed: Issues with new series layout #360
* Fixed: Don't override taxonomy template when layout is default #349



v2.7.2- 2022-01-05
* Fixed: Make text easier to read #348
* Fixed: Overview page layout description #346
* Fixed: Don't override taxonomy template when layout is default #349
* Fixed: Add capability check for saving options
* Fixed: Santize all form entries
* Fixed: Add and validate all form nonce

v2.7.1- 2021-12-09
* Fixed: PHP notices from the widget #303
* Fixed: Visual bug with the "Series Taxonomy" choice #312
* Fixed: Deprecated notices #307
* Added: Capability to access the "Series" menu #313
* Fixed: "Show Series Table Of Content" setting is broken in widget #308
* Added: Allow users to customize buttons #311
* Added: "Click to view" for "Series Table of Contents URL" #320
* Added: Add series group template to PRO version group addon #202
* Fixed: Sidebar warning for theme without sidebar.php on custom template #335
* Fixed: Improved design for Series overview page #309
* Added: Bulk edit for series #338

v2.7.0- 2021-11-17
* Fixed: Clarify what HTML is allowed in templates #273
* Fixed: Update the Reviews box class #274
* Fixed: Add message to draft series without part number #261
* Feature: Added %first_post% token to series navigation #232
* Fixed: Incorrect Yoast SEO attributes for Series TOC page #82
* Added: New menu link for Series #278
* Added: Post list series limit #271
* Feature: Allow users to change the navigation, metabox or post series box position #270
* Feature: Improve the "Series Table of Content" widge #238
* Fixed: wp_serieslist_display() Not Working #287
* Fixed: Issue with Seriously Simple Podcasting plugin #87
* Fixed: PHP Notice: Trying to access array offse #298

v2.6.3- 2021-09-29
* Fixed: Fix "invalid_taxonomy" WP_Error #88
* Fixed: Append to series on action Scheduled to Published #83
* Fixed: Fix "Only variable references should be returned by reference" #89
* Fixed: Edit Series screen has bold text over-run #225
* Fixed: More clarity on "Templates" tab #226
* Fixed: Template previous button arrows are in the wrong direction and not saving #233
* Fixed: Show the ID in Manage Series #237
* Fixed: Metabox is not entirely clear with Multiple Series #229
* Fixed: Larger boxes for Templates area #236
* Feature: Allow "Table of Contents" widget to show a specified series #234
* Added: Add a review request #210
* Added: Include Italian translation #253
* Fixed: Series Grouping using categories #216

v2.6.2- 2021-09-07
* Fixed: Yoast SEO issue #206
* Fixed: Move "Reset options" to uninstall tab #208
* Fixed: Improve settings option labels #205
* Fixed: 2.6.1 is causing a JavaScript error in core #213
* Fixed: Can't add icon/image to a series #187

v2.6.1- 2021-09-02
* Fixed: More room on settings pages #183
* Fixed: Clean up for icons screen #181
* Fixed: Series page shows "No tags found." #182
* Fixed: Small design issues in metabox #160
* Fixed: Multiple series list overlays fields #188
* Fixed: Make plugin translatable #186
* Fixed: Quick Edit problem with Multiples #196
* Fixed: Changes to 'General' Settings #199
* Fixed: Formatting issues with Latest Series Widget #198

v2.6.0- 2021-08-26
* Updated: New name and remove previous sidebar branding #98
* Fixed: Clean up sidebar box in post editingg screen #99
* Fixed: Fix "Use of undefined constant SERIES_QUERYVAR" error #86
* Fixed: PHP Notice: Trying to access array offset on value of type null #117
* Fixed: Edit series error #126
* Fixed: Series settings screen cleanup #129
* Fixed: PHP Notice: Undefined index notices on settings update #144
* Fixed: PHP Notice: Trying to access array offset on value of type null on series single page #147
* Fixed: Improve CSS on frontend #162
* Fixed: Use new Media Library for series icon selection #141
* Fixed: Series TOC Widget improvements #163
* Fixed: Latest Series widget doesn't load #164
* Added: Package add-ons into Pro version #101
* Added: Add Upgrade banner for Pro version #100
* Added: Add the PublishPress Footer to free version #97

For older commits, commits see the [commit list on Github](https://github.com/publishpress/publishpress-series/commits/master) or the [release tags](https://github.com/publishpress/publishpress-series/releases). You can also view the [closed milestones on github](https://github.com/publishpress/publishpress-series/milestones?state=closed) to see what changed within a release.
