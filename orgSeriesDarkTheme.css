.seriesbox {
	background: #222;
	color: #f2f2f2;
	text-align: left;
	font-size: .8em;
	margin: 0 10px 0px 10px;
	padding: 0 20px 20px;
	margin-right: 0px;
	min-width: 125px;
}
.seriesbox ul.serieslist-ul {
	padding-left: 20px;
}
.seriesbox img {
	padding-top: 20px;
}
.seriesmeta {
	background-color: #222;
	font-size: .8em;
	color: #f2f2f2;
	display: block;
	padding: 20px;
	margin-bottom: 20px;
}
.seriesbox a,
.seriesmeta a {
	color: #f2f2f2;
	text-decoration: underline;
}
.seriesbox a:hover,
.seriesbox a:focus,
.seriesbox a:active,
.seriesmeta a:hover,
.seriesmeta a:focus,
.seriesmeta a:active {
	color: #fff;
}

.series-nav-left {
    margin-right: 20px;
}

@media (min-width:979px) {
	body:not([class*="theme-twentytwenty"]) .series-nav-left {
		float: left;
	}
	body:not([class*="theme-twentytwenty"]) .series-nav-right {
		float: right;
	}
}
