<?php
/**
 * Debug page to verify template preview functionality
 * Access this file directly to test if the preview functions work
 */

// Include WordPress
if (file_exists('../../../wp-config.php')) {
    require_once('../../../wp-config.php');
} else {
    die('WordPress not found. Please check the path.');
}

// Check if we're on the right page
if (!current_user_can('manage_publishpress_series')) {
    die('You do not have permission to access this page.');
}

echo "<h1>PublishPress Series Template Preview Debug</h1>";

// Check if functions exist
echo "<h2>Function Checks:</h2>";
echo "<ul>";
echo "<li>ppseries_get_sample_preview_data: " . (function_exists('ppseries_get_sample_preview_data') ? '✅ EXISTS' : '❌ MISSING') . "</li>";
echo "<li>ppseries_process_template_preview: " . (function_exists('ppseries_process_template_preview') ? '✅ EXISTS' : '❌ MISSING') . "</li>";
echo "<li>ppseries_template_preview_ajax: " . (function_exists('ppseries_template_preview_ajax') ? '✅ EXISTS' : '❌ MISSING') . "</li>";
echo "</ul>";

// Check if AJAX action is registered
echo "<h2>AJAX Action Check:</h2>";
global $wp_filter;
$ajax_registered = isset($wp_filter['wp_ajax_ppseries_template_preview']);
echo "<p>wp_ajax_ppseries_template_preview: " . ($ajax_registered ? '✅ REGISTERED' : '❌ NOT REGISTERED') . "</p>";

// Test sample data generation
if (function_exists('ppseries_get_sample_preview_data')) {
    echo "<h2>Sample Data Test:</h2>";
    $sample_data = ppseries_get_sample_preview_data();
    echo "<pre style='background: #f0f0f0; padding: 10px; border-radius: 4px;'>";
    print_r($sample_data);
    echo "</pre>";
}

// Test template processing
if (function_exists('ppseries_process_template_preview') && function_exists('ppseries_get_sample_preview_data')) {
    echo "<h2>Template Processing Test:</h2>";
    
    $sample_data = ppseries_get_sample_preview_data();
    
    // Test Series Meta Template
    $meta_template = '<div class="seriesmeta">This entry is part %series_part% of %total_posts_in_series% in the series %series_title_linked%</div>';
    echo "<h3>Series Meta Template:</h3>";
    echo "<strong>Input:</strong> <code>" . htmlspecialchars($meta_template) . "</code><br>";
    echo "<strong>Output:</strong><br>";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #fff; margin: 10px 0;'>";
    echo ppseries_process_template_preview($meta_template, 'series_meta_template', $sample_data);
    echo "</div>";
    
    // Test Series Post List Template
    $post_list_template = '<div class="seriesbox"><div class="center">%series_title_linked%</div><ul class="serieslist-ul">%post_title_list%</ul></div>';
    echo "<h3>Series Post List Template:</h3>";
    echo "<strong>Input:</strong> <code>" . htmlspecialchars($post_list_template) . "</code><br>";
    echo "<strong>Output:</strong><br>";
    echo "<div style='border: 1px solid #ddd; padding: 10px; background: #fff; margin: 10px 0;'>";
    echo ppseries_process_template_preview($post_list_template, 'series_post_list_template', $sample_data);
    echo "</div>";
}

// Check if CSS file exists
echo "<h2>File Checks:</h2>";
$css_file = PPSERIES_PATH . 'assets/css/pressshack-admin.css';
echo "<p>CSS File: " . ($css_file && file_exists($css_file) ? '✅ EXISTS' : '❌ MISSING') . " ($css_file)</p>";

$js_file = PPSERIES_PATH . 'js/orgseries_options.js';
echo "<p>JS File: " . ($js_file && file_exists($js_file) ? '✅ EXISTS' : '❌ MISSING') . " ($js_file)</p>";

// Check WordPress admin page
echo "<h2>Admin Page Check:</h2>";
echo "<p>Current page: " . (isset($_GET['page']) ? $_GET['page'] : 'Not set') . "</p>";
echo "<p>Is admin: " . (is_admin() ? '✅ YES' : '❌ NO') . "</p>";
echo "<p>Is series admin page: " . (function_exists('is_ppseries_admin_pages') && is_ppseries_admin_pages() ? '✅ YES' : '❌ NO') . "</p>";

// Instructions
echo "<h2>How to Test:</h2>";
echo "<ol>";
echo "<li>Go to your WordPress admin: <a href='" . admin_url('admin.php?page=orgseries_options_page') . "' target='_blank'>Series Settings</a></li>";
echo "<li>Click on the <strong>Templates</strong> tab (third tab)</li>";
echo "<li>Look for preview boxes above the template input fields</li>";
echo "<li>Try editing a template - the preview should update automatically</li>";
echo "<li>Open browser console (F12) to see debug messages</li>";
echo "</ol>";

// Add some basic CSS for better display
echo "<style>";
echo "body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; }";
echo "h1, h2, h3 { color: #333; }";
echo "code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }";
echo "ul, ol { line-height: 1.6; }";
echo ".seriesmeta { background: #f0f8ff; border-left: 4px solid #0073aa; padding: 12px 16px; margin: 10px 0; }";
echo ".seriesmeta a { color: #0073aa; text-decoration: none; }";
echo ".seriesbox { border: 1px solid #ddd; padding: 20px; background: #fff; }";
echo ".serieslist-ul { list-style: none; padding: 0; }";
echo ".serieslist-li, .serieslist-li-current { padding: 8px 0; border-bottom: 1px solid #eee; }";
echo ".serieslist-li-current { font-weight: bold; }";
echo "</style>";
?>
