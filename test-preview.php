<?php
/**
 * Simple test file to verify template preview functionality
 * This file can be accessed directly to test the preview functions
 */

// Include WordPress
require_once('../../../wp-config.php');

// Include the main plugin file to get access to our functions
require_once('orgSeries-options.php');

// Test the sample data generation
echo "<h2>Testing Sample Data Generation</h2>";
$sample_data = ppseries_get_sample_preview_data();
echo "<pre>";
print_r($sample_data);
echo "</pre>";

// Test template processing
echo "<h2>Testing Template Processing</h2>";

// Test Series Meta Template
$meta_template = '<div class="seriesmeta">This entry is part %series_part% of %total_posts_in_series% in the series %series_title_linked%</div>%postcontent%';
echo "<h3>Series Meta Template:</h3>";
echo "<strong>Template:</strong> " . htmlspecialchars($meta_template) . "<br>";
echo "<strong>Preview:</strong><br>";
echo ppseries_process_template_preview($meta_template, 'series_meta_template', $sample_data);

echo "<hr>";

// Test Series Post List Template
$post_list_template = '<div class="seriesbox"><div class="center">%series_title_linked%</div><ul class="serieslist-ul">%post_title_list%</ul></div>%postcontent%';
echo "<h3>Series Post List Template:</h3>";
echo "<strong>Template:</strong> " . htmlspecialchars($post_list_template) . "<br>";
echo "<strong>Preview:</strong><br>";
echo ppseries_process_template_preview($post_list_template, 'series_post_list_template', $sample_data);

echo "<hr>";

// Test Series Navigation Template
$nav_template = '%postcontent%<fieldset><legend>Series Navigation</legend><span class="series-nav-left">%previous_post%</span><span class="series-nav-right">%next_post%</span></fieldset>';
echo "<h3>Series Navigation Template:</h3>";
echo "<strong>Template:</strong> " . htmlspecialchars($nav_template) . "<br>";
echo "<strong>Preview:</strong><br>";
echo ppseries_process_template_preview($nav_template, 'series_post_nav_template', $sample_data);

echo "<hr>";

// Test Table of Contents Template
$toc_template = '<div class="serieslist-box"><div class="serieslist-content"><h2>%series_title_linked%</h2><p>%series_description%</p></div><hr style="clear: left; border: none" /></div>';
echo "<h3>Table of Contents Template:</h3>";
echo "<strong>Template:</strong> " . htmlspecialchars($toc_template) . "<br>";
echo "<strong>Preview:</strong><br>";
echo ppseries_process_template_preview($toc_template, 'series_table_of_contents_box_template', $sample_data);

echo "<style>";
echo file_get_contents('assets/css/pressshack-admin.css');
echo "</style>";
?>
