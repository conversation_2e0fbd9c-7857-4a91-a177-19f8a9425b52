.seriesbox {
	background: #f2f2f2;
	color: #444;
	text-align: left;
	font-size: .8em;
	margin: 0 10px 0px 10px;
	padding: 0 20px 20px;
	border: 1px solid #ccc;
	margin-right: 0px;
	min-width: 125px;
}
.seriesbox img {
	padding-top: 20px;
}
.seriesbox ul.serieslist-ul {
	padding-left: 20px;
}
.seriesmeta {
	background-color: #f2f2f2;
	font-size: .8em;
	color: #444;
	display: block;
	padding: 20px;
	margin-bottom: 20px;
	border: 1px solid #ccc;
}
.seriesbox a,
.seriesmeta a {
	color: #444;
	text-decoration: underline;
}
.seriesbox a:hover,
.seriesbox a:focus,
.seriesbox a:active,
.seriesmeta a:hover,
.seriesmeta a:focus,
.seriesmeta a:active {
	color: #000;
}

.series-nav-left {
    margin-right: 20px;
}

@media (min-width:979px) {
	body:not([class*="theme-twentytwenty"]) .series-nav-left {
		float: left;
	}
	body:not([class*="theme-twentytwenty"]) .series-nav-right {
		float: right;
	}
}
