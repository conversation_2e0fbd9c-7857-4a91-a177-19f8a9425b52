{"name": "organize-series", "version": "1.0.0", "description": "Assets for the Publishpress Series WordPress plugin", "main": "webpack.dev.js", "scripts": {"watch": "webpack --watch --config webpack.dev.js", "build": "webpack --config webpack.prod.js", "watch-poll": "webpack --watch --watch-poll --config webpack.dev.js", "compile_css": "node-sass --output-style compressed scss/ --output=css/ --watch"}, "author": "<PERSON>", "license": "GPL-2.0", "browserslist": ["extends @wordpress/browserslist-config"], "dependencies": {"@wordpress/dom-ready": "^4.19.0", "ansi-regex": "^2.1.1", "axios": "^1.8.2", "qs": "^6.9.7"}, "devDependencies": {"@wordpress/browserslist-config": "^2.6.0", "assets-webpack-plugin": "^3.9.10", "autoprefixer": "^10.4.14", "babel-core": "^6.26.3", "babel-loader": "^7.1.5", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "clean-webpack-plugin": "^0.1.19", "extract-text-webpack-plugin": "^3.0.2", "node-sass": "^9.0.0", "postcss-loader": "^7.3.0", "uglifyjs-webpack-plugin": "^0.4.6", "webpack": "^5.98.0", "webpack-combine-loaders": "^2.0.4", "webpack-merge": "^4.2.2"}}